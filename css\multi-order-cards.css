/* 多订单卡片样式 - 直立卡片设计，粉红主题配色，移动端优化 */

/* CSS变量定义 - 品牌紫色主题配色系统 */
:root {
    --primary-brand: #9F299F;
    --primary-brand-light: #B84CB8;
    --primary-brand-dark: #7A1F7A;
    --primary-brand-bg: #F4E8F4;
    --primary-brand-bg-light: #F8F0F8;
    --primary-brand-transparent: rgba(159, 41, 159, 0.57);
    --primary-brand-hover: rgba(184, 76, 184, 0.2);
    --primary-brand-selected: rgba(159, 41, 159, 0.1);
}

/* 多订单面板基础样式 */
.multi-order-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
    display: none; /* 默认隐藏 */
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.multi-order-panel:not(.hidden) {
    display: flex !important;
}

.multi-order-content {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(159, 41, 159, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 多订单头部样式 */
.multi-order-header {
    background: linear-gradient(135deg, var(--primary-brand) 0%, var(--primary-brand-light) 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.multi-order-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.multi-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.order-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.order-count {
    font-weight: 600;
    font-size: 1rem;
}

.date-range {
    font-size: 0.85rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-actions .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
}

/* 批量控制面板 - 紧凑设计 */
.batch-control-panel {
    background: linear-gradient(135deg, var(--primary-brand-light) 0%, var(--primary-brand) 100%);
    color: rgba(255, 255, 255, 0.381);
    padding: 12px 16px;
}

.batch-control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.batch-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;
}

.batch-icon {
    font-size: 18px;
}

/* 下拉菜单设计 */
.batch-dropdown {
    position: relative;
}

.batch-dropdown-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.148);
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.batch-dropdown-btn:hover {
    background: var(--primary-brand-hover);
    border-color: rgba(255, 255, 255, 0.5);
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.batch-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 16px;
    min-width: 280px;
    z-index: 1001;
    display: none;
    color: #333;
}

.batch-option {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.batch-option:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.batch-option-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.batch-option-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 8px;
}

.batch-apply-btn {
    background: linear-gradient(135deg, var(--primary-brand-light) 0%, var(--primary-brand) 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.batch-apply-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 12px var(--primary-brand-selected);
}

/* 批量操作按钮区域 */
.batch-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding-top: 16px;
    border-top: 2px solid #f0f0f0;
}

.batch-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-create-all {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    grid-column: 1 / -1; /* 跨两列 */
}

.btn-select-all {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-clear-all {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.batch-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.batch-summary {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

/* 订单卡片网格 */
.multi-order-list {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    background: rgba(248, 249, 250, 0.8);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    min-height: 300px;
}

/* 多订单底部样式 */
.multi-order-footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(159, 41, 159, 0.2);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.creation-summary {
    display: flex;
    align-items: center;
    gap: 12px;
}

.creation-summary span {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.batch-create-status {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    border-top: 1px solid rgba(159, 41, 159, 0.1);
    border-bottom: 1px solid rgba(159, 41, 159, 0.1);
    font-size: 0.9rem;
    text-align: center;
}

/* 直立卡片设计 */
.order-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 2px solid transparent;
    position: relative;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 0 20px var(--primary-brand-selected);
}

.order-card.selected {
    border-color: var(--primary-brand);
    background: linear-gradient(135deg, var(--primary-brand-bg) 0%, var(--primary-brand-bg-light) 100%);
}

.order-card.paging-order {
    border-left: 4px solid #ffc107;
}

/* 卡片头部 */
.order-card-header {
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--primary-brand-light) 0%, var(--primary-brand) 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: white;
}

.order-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-number {
    font-weight: 600;
    font-size: 14px;
}

.paging-badge {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.status-ready {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

/* 卡片主体 */
.order-card-body {
    padding: 16px;
    cursor: pointer;
}

.order-card-body:hover {
    background: var(--primary-brand-bg);
}

/* 卡片底部操作区 */
.order-card-footer {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

.btn-card-action {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
}

.btn-create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    width: 100%;
}

.btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 600;
}

/* 移动端优化 - 方案一：紧凑高效型 */

/* 平板端优化 (768px-481px) */
@media (max-width: 768px) and (min-width: 481px) {
    .multi-order-panel {
        padding: 12px;
    }

    .batch-control-panel {
        padding: 10px 16px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .batch-title {
        font-size: 15px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 44px;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.25);
    }

    .batch-dropdown-content {
        min-width: 280px;
        padding: 16px;
    }

    .multi-order-list {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        padding: 12px;
    }

    .order-card-header {
        padding: 12px 14px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .order-card-body {
        padding: 14px;
    }

    .order-card-footer {
        padding: 12px 14px;
    }

    .btn-card-action {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 44px;
        font-weight: 600;
    }

    .batch-actions {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .btn-create-all {
        grid-column: 1 / -1;
        min-height: 44px;
    }
}

/* 大屏手机端优化 (480px-376px) - 智能双列布局 */
@media (max-width: 480px) and (min-width: 376px) {
    .multi-order-panel {
        padding: 8px;
    }

    .batch-control-panel {
        padding: 8px 12px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
    }

    .batch-title {
        font-size: 14px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-height: 44px;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.25);
    }

    .batch-dropdown-content {
        min-width: 260px;
        padding: 12px;
    }

    .multi-order-list {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        padding: 8px;
    }

    .order-card {
        font-size: 13px;
    }

    .order-card-header {
        padding: 10px 12px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
    }

    .order-checkbox {
        width: 20px;
        height: 20px;
        accent-color: white;
    }

    .order-card-body {
        padding: 12px;
    }

    .order-card-footer {
        padding: 10px 12px;
    }

    .btn-card-action {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 44px;
        font-weight: 600;
    }

    .batch-actions {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .batch-action-btn {
        min-height: 44px;
        font-size: 13px;
        font-weight: 600;
    }

    .btn-create-all {
        grid-column: 1;
    }
}

/* 小屏手机端优化 (375px以下) - 单列紧凑布局 */
@media (max-width: 375px) {
    .multi-order-panel {
        padding: 6px;
    }

    .batch-control-panel {
        padding: 8px 10px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    }

    .batch-title {
        font-size: 13px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 44px;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.25);
        width: 100%;
        justify-content: space-between;
    }

    .batch-dropdown-content {
        min-width: calc(100vw - 32px);
        left: 0;
        right: 0;
        margin: 0 16px;
        padding: 12px;
    }

    .multi-order-list {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 8px;
    }

    .order-card {
        font-size: 13px;
    }

    .order-card-header {
        padding: 12px;
        background: linear-gradient(135deg, var(--primary-brand-dark) 0%, var(--primary-brand) 100%);
        color: white;
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    }

    .order-number {
        font-size: 13px;
        font-weight: 600;
    }

    .order-checkbox {
        width: 22px;
        height: 22px;
        accent-color: white;
    }

    .order-card-body {
        padding: 12px;
    }

    .order-card-footer {
        padding: 12px;
    }

    .btn-card-action {
        padding: 12px 16px;
        font-size: 13px;
        min-height: 48px;
        font-weight: 600;
        border-radius: 8px;
    }

    .batch-actions {
        grid-template-columns: 1fr;
        gap: 8px;
        padding-top: 12px;
    }

    .batch-action-btn {
        min-height: 48px;
        font-size: 14px;
        font-weight: 600;
        padding: 12px 16px;
    }

    .btn-create-all {
        grid-column: 1;
    }

    /* 优化批量操作按钮间距 */
    .batch-option {
        margin-bottom: 12px;
        padding-bottom: 12px;
    }

    .batch-option-select {
        min-height: 44px;
        font-size: 14px;
    }

    .batch-apply-btn {
        min-height: 44px;
        font-size: 13px;
        padding: 10px 16px;
    }
}

/* 极小屏幕优化 (320px以下) */
@media (max-width: 320px) {
    .multi-order-panel {
        padding: 4px;
    }

    .batch-control-panel {
        padding: 6px 8px;
    }

    .batch-title {
        font-size: 12px;
    }

    .batch-dropdown-btn {
        padding: 6px 10px;
        font-size: 11px;
        min-height: 44px;
    }

    .order-card-header {
        padding: 10px;
    }

    .order-card-body {
        padding: 10px;
    }

    .order-card-footer {
        padding: 10px;
    }

    .btn-card-action {
        padding: 10px 12px;
        font-size: 12px;
        min-height: 44px;
    }

    .batch-action-btn {
        min-height: 44px;
        font-size: 12px;
        padding: 10px 12px;
    }
}

/* 移动端专用增强样式 */
@media (max-width: 768px) {
    /* 高对比度文字增强 */
    .batch-control-panel {
        color: white !important;
        text-shadow: 0 1px 3px rgba(0,0,0,0.6);
    }

    .order-card-header {
        color: white !important;
        text-shadow: 0 1px 3px rgba(0,0,0,0.6);
    }

    /* 选中状态增强 */
    .order-card.selected {
        border-color: var(--primary-brand-dark);
        border-width: 3px;
        background: linear-gradient(135deg, var(--primary-brand-bg) 0%, var(--primary-brand-bg-light) 100%);
        box-shadow: 0 2px 12px var(--primary-brand-selected);
    }

    /* 触摸反馈增强 */
    .btn-card-action:active,
    .batch-action-btn:active,
    .batch-apply-btn:active {
        transform: translateY(1px) scale(0.98);
        transition: all 0.1s ease;
    }

    /* 复选框增强 */
    .order-checkbox {
        cursor: pointer;
        transform: scale(1.1);
    }

    .order-checkbox:checked {
        accent-color: var(--primary-brand);
    }

    /* 下拉菜单移动端优化 */
    .batch-dropdown-content {
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    /* 按钮间距优化 */
    .batch-actions {
        margin-top: 8px;
    }

    .batch-action-btn + .batch-action-btn {
        margin-top: 0;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .multi-order-panel {
        padding: 8px 16px;
    }

    .batch-control-panel {
        padding: 6px 16px;
    }

    .multi-order-list {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
    }

    .order-card-header {
        padding: 8px 12px;
    }

    .order-card-body {
        padding: 10px 12px;
    }

    .order-card-footer {
        padding: 8px 12px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .batch-control-panel,
    .order-card-header {
        background: var(--primary-brand-dark) !important;
        color: white !important;
        text-shadow: none;
    }

    .order-card.selected {
        border-color: var(--primary-brand-dark);
        border-width: 4px;
    }

    .btn-card-action {
        border: 2px solid var(--primary-brand-dark);
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease-out;
}

.batch-dropdown-content {
    animation: slideIn 0.2s ease-out;
}

/* 关闭按钮样式 */
.multi-order-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    z-index: 1002;
    transition: all 0.2s ease;
}

.multi-order-close-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

/* 移动端关闭按钮优化 */
@media (max-width: 768px) {
    .multi-order-close-btn {
        top: 12px;
        right: 12px;
        width: 44px;
        height: 44px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.6);
        -webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .multi-order-close-btn:active {
        transform: scale(0.95);
        background: rgba(0, 0, 0, 0.8);
    }
}

@media (max-width: 375px) {
    .multi-order-close-btn {
        top: 8px;
        right: 8px;
        width: 48px;
        height: 48px;
        font-size: 22px;
    }
}