/* CSS 自定义属性 - Neumorphism紫色主题设计 */
:root {
  /* 基于公司logo #9F299F的品牌色彩系统 */
  --color-primary: #9F299F;
  --color-primary-hover: #B84CB8;
  --color-primary-light: #C166C1;
  --color-primary-gradient: linear-gradient(135deg, #9F299F 0%, #B84CB8 50%, #7A1F7A 100%);

  /* 辅助色 */
  --color-secondary: #8E8E93;
  --color-secondary-hover: #6B6B70;
  --color-secondary-light: #F0F0F3;

  /* 状态色 */
  --color-success: #4CAF50;
  --color-success-light: #E8F5E8;
  --color-warning: #FF9800;
  --color-warning-light: #FFF3E0;
  --color-error: #F44336;
  --color-error-light: #FFEBEE;
  --color-info: #2196F3;
  --color-info-light: #E3F2FD;

  /* Neumorphism背景色系 */
  --color-white: #FFFFFF;
  --color-neu-bg: #F0F0F3;        /* 主背景 */
  --color-neu-bg-secondary: #E6E6EA;  /* 次背景 */
  --color-neu-card: #FAFAFA;      /* 卡片背景 */
  --color-neu-light: #FFFFFF;     /* 高光色 */
  --color-neu-shadow: #D1D1D6;    /* 阴影色 */
  --color-neu-border: #E0E0E3;    /* 边框色 */

  /* 中性色 - 调整为适合Neumorphism */
  --color-gray-50: #F8F8FA;
  --color-gray-100: #F0F0F3;
  --color-gray-200: #E6E6EA;
  --color-gray-300: #D1D1D6;
  --color-gray-400: #A8A8B0;
  --color-gray-500: #8E8E93;
  --color-gray-600: #6B6B70;
  --color-gray-700: #48484A;
  --color-gray-800: #2D2D30;
  --color-gray-900: #1C1C1E;

  /* 背景色 */
  --bg-primary: var(--color-neu-bg);
  --bg-secondary: var(--color-neu-bg-secondary);
  --bg-tertiary: var(--color-neu-card);

  /* 文字色 */
  --text-primary: var(--color-gray-800);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-accent: var(--color-primary);

  /* 边框色 */
  --border-color: var(--color-neu-border);
  --border-hover: var(--color-gray-300);

  /* 新品牌毛玻璃效果系统 */
  --brand-glass: rgba(159, 41, 159, 0.1);
  --shadow-card: 0 10px 25px -5px rgba(159, 41, 159, 0.1);
  --blur-glass: blur(16px);

  /* 传统阴影（备用） */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 品牌毛玻璃效果 */
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-card: #FFFFFF;
  --glass-border: rgba(159, 41, 159, 0.1);
  
  /* 紧凑化间距系统 - 大幅减少所有间距 */
  --spacing-1: 0.125rem;  /* 2px - 原0.25rem */
  --spacing-2: 0.25rem;   /* 4px - 原0.5rem */
  --spacing-3: 0.375rem;  /* 6px - 原0.75rem */
  --spacing-4: 0.5rem;    /* 8px - 原1rem */
  --spacing-5: 0.625rem;  /* 10px - 原1.25rem */
  --spacing-6: 0.75rem;   /* 12px - 原1.5rem */
  --spacing-8: 1rem;      /* 16px - 原2rem */
  --spacing-10: 1.25rem;  /* 20px - 原2.5rem */
  --spacing-12: 1.5rem;   /* 24px - 原3rem */
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 字体 */
  --font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 行高 */
  --line-height-tight: 1.2;   /* 更紧凑的行高 */
  --line-height-normal: 1.4;  /* 减少行高 */
  --line-height-relaxed: 1.6; /* 减少行高 */

  /* 动态高度变量 */
  --header-height: 80px;
  --grid-height: calc(100vh - 200px);
  --grid-columns: 1fr 1fr;
  --grid-rows: 1fr 1fr;
}

/* ========== 新毛玻璃卡片样式系统 ========== */

/* 紧凑同行布局样式 */
.compact-inline-layout {
  --item-height: 24px;
  --item-spacing: 4px;
  --font-size: 11px;
  --line-height: 1.1;
}

.inline-item {
  display: inline-flex;
  align-items: center;
  height: var(--item-height);
  margin-right: var(--item-spacing);
  font-size: var(--font-size);
  line-height: var(--line-height);
  white-space: nowrap;
}

.inline-label {
  font-weight: 600;
  margin-right: 2px;
  color: var(--color-primary);
}

.inline-value {
  color: var(--text-secondary);
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 200px;
}

/* 毛玻璃卡片样式 */
.compact-card {
  padding: 8px 10px;
  margin-bottom: 6px;
  border-radius: 12px;
  background: var(--bg-glass);
  backdrop-filter: var(--blur-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--glass-border);
  min-height: auto;
  height: auto;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.compact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px -5px rgba(159, 41, 159, 0.2);
  border-color: var(--color-primary);
}

/* 三列布局系统 */
.three-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  height: calc(100vh - 120px);
  padding: 8px;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 三列布局的列容器 */
.three-column-layout .column-mobile {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}

/* 三列移动端布局 */
.three-column-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 4px;
  height: calc(100vh - 70px);
  overflow: hidden;
  padding: 4px;
}

.column-mobile {
  overflow-y: auto;
  padding: 4px;
  border-radius: 8px;
  background: var(--brand-glass);
}

.column-mobile::-webkit-scrollbar {
  width: 3px;
}

.column-mobile::-webkit-scrollbar-track {
  background: transparent;
}

.column-mobile::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 3px;
}

/* 状态图标样式 */
.status-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 2px;
  vertical-align: middle;
}

.status-complete { color: #22c55e; }
.status-progress { color: #f59e0b; }
.status-cancel { color: #ef4444; }
.status-normal { color: #22c55e; }
.status-warning { color: #f59e0b; }
.status-error { color: #ef4444; }

/* 全局防止水平滚动 */
body, html {
  overflow-x: hidden;
  max-width: 100vw;
}

/* 紧凑同行表单样式 */
.compact-inline-layout .form-group {
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  min-height: var(--item-height);
  width: 100%;
  box-sizing: border-box;
}

.compact-inline-layout .form-group label {
  display: inline-block;
  min-width: 80px;
  margin-right: var(--spacing-2);
  font-size: var(--font-size);
  font-weight: 600;
  color: var(--color-primary);
  white-space: nowrap;
  flex-shrink: 0;
}

.compact-inline-layout .form-group input,
.compact-inline-layout .form-group select,
.compact-inline-layout .form-group textarea {
  flex: 1;
  font-size: var(--font-size);
  line-height: var(--line-height);
  padding: 2px 4px;
  min-height: 20px;
  min-width: 0;
  border: 1px solid var(--glass-border);
  border-radius: 4px;
  box-sizing: border-box;
}

.compact-inline-layout .form-group textarea {
  max-height: 60px;
  resize: vertical;
}

/* 紧凑上传按钮样式 */
.compact-upload-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.btn-compact-upload {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid var(--glass-border);
  border-radius: 4px;
  background: var(--bg-glass);
  color: var(--color-primary);
  font-size: var(--font-size);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 0;
}

.btn-compact-upload:hover {
  background: var(--brand-glass);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.btn-compact-upload .upload-icon {
  font-size: 12px;
}

.btn-compact-upload .upload-text {
  font-size: var(--font-size);
  white-space: nowrap;
}

/* 响应式断点优化 */
@media (max-width: 1200px) {
  .three-column-layout {
    gap: 6px;
    padding: 6px;
  }
  .compact-card { 
    padding: 7px 9px;
  }
  .inline-item { font-size: 10px; }
}

@media (max-width: 992px) {
  .three-column-layout {
    gap: 5px;
    padding: 5px;
  }
  .compact-card {
    padding: 6px 8px;
  }
}

@media (max-width: 768px) {
  .three-column-layout {
    height: calc(100vh - 100px);
    gap: 4px;
    padding: 4px;
  }
  .compact-card {
    padding: 5px 7px;
    margin-bottom: 4px;
  }
  .inline-item {
    font-size: 9px;
    --item-height: 20px;
  }
  .compact-inline-layout .form-group label {
    min-width: 60px;
    font-size: 9px;
  }
  .compact-inline-layout .form-group input,
  .compact-inline-layout .form-group select,
  .compact-inline-layout .form-group textarea {
    font-size: 9px;
    padding: 1px 3px;
  }
}

@media (max-width: 480px) {
  .three-column-layout {
    gap: 3px;
    padding: 3px;
    height: calc(100vh - 90px);
  }
  .compact-card {
    padding: 4px 6px;
    margin-bottom: 3px;
  }
  .compact-inline-layout .form-group {
    margin-bottom: 1px;
  }
}

@media (max-width: 375px) {
  .inline-item {
    font-size: 8px;
    --item-height: 18px;
  }
  .compact-card { 
    padding: 3px 5px; 
  }
  .compact-inline-layout .form-group label {
    min-width: 50px;
    font-size: 8px;
  }
  .compact-inline-layout .form-group input,
  .compact-inline-layout .form-group select,
  .compact-inline-layout .form-group textarea {
    font-size: 8px;
    padding: 1px 2px;
  }
}

/* 暗色主题 - 紫色Neumorphism协调配色 */
[data-theme="dark"] {
  /* 深紫色背景色系 */
  --color-neu-bg-dark: #1a0d1f;        /* 主背景 - 深紫黑 */
  --color-neu-bg-secondary-dark: #2d1b3d;  /* 次背景 - 深紫 */
  --color-neu-card-dark: #3d2a4a;      /* 卡片背景 - 中紫 */
  --color-neu-light-dark: #4a3357;     /* 高光色 - 亮紫 */
  --color-neu-shadow-dark: #0f0612;    /* 阴影色 - 极深紫 */
  --color-neu-border-dark: #5a4067;    /* 边框色 - 紫灰 */

  /* 应用暗色主题配色 */
  --bg-primary: var(--color-neu-bg-dark);
  --bg-secondary: var(--color-neu-bg-secondary-dark);
  --bg-tertiary: var(--color-neu-card-dark);

  /* 高对比度文字色 */
  --text-primary: #f0e6f7;    /* 浅紫白 - 主文字 */
  --text-secondary: #d1c4dd;  /* 紫灰 - 次要文字 */
  --text-tertiary: #a888b5;   /* 中紫灰 - 辅助文字 */
  --text-accent: #ff8cff;     /* 亮粉紫 - 强调色 */

  /* 边框色 */
  --border-color: var(--color-neu-border-dark);
  --border-hover: #6b4d78;

  /* 暗色主题专用Neumorphism阴影 */
  --neu-shadow-outset: 8px 8px 16px var(--color-neu-shadow-dark), -8px -8px 16px var(--color-neu-light-dark);
  --neu-shadow-inset: inset 4px 4px 8px var(--color-neu-shadow-dark), inset -4px -4px 8px var(--color-neu-light-dark);
  --neu-shadow-pressed: inset 8px 8px 16px var(--color-neu-shadow-dark), inset -8px -8px 16px var(--color-neu-light-dark);
  --neu-shadow-hover: 12px 12px 24px var(--color-neu-shadow-dark), -12px -12px 24px var(--color-neu-light-dark);
  --neu-shadow-subtle: 4px 4px 8px var(--color-neu-shadow-dark), -4px -4px 8px var(--color-neu-light-dark);

  /* 暗色毛玻璃效果 */
  --glass-bg: rgba(61, 42, 74, 0.8);
  --glass-bg-dark: rgba(26, 13, 31, 0.9);
  --glass-border: var(--color-neu-border-dark);
  --glass-border-dark: rgba(240, 230, 247, 0.1);
  --glass-shadow: var(--neu-shadow-outset);
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-primary);
  background-image: radial-gradient(circle at 20% 50%, rgba(247, 92, 244, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(224, 64, 251, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 40% 80%, rgba(213, 0, 249, 0.05) 0%, transparent 50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; /* 只隐藏水平滚动条 */
  overflow-y: auto; /* 允许垂直滚动 */
  min-height: 100vh; /* 最小高度，允许内容扩展 */
}

/* 布局容器 - 动态高度适配 */
#app {
  min-height: 100vh; /* 改为最小高度 */
  height: auto; /* 允许内容驱动 */
  display: flex;
  flex-direction: column;
  overflow: visible; /* 允许内容可见 */
}

.app-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-3) 0; /* 减少头部padding */
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-xl);  /* 减少标题大小 */
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.title-icon {
  font-size: var(--font-size-2xl);  /* 减少图标大小 */
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少头部控件间距 */
  flex-wrap: wrap;        /* 允许换行以适应小屏幕 */
  min-width: 0;          /* 允许收缩 */
}

/* 移动端导航栏优化 - 保持水平布局 */
@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-2) 0; /* 减少移动端头部padding */
  }
  
  .header-content {
    flex-direction: row; /* 保持水平布局 */
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-1);
    padding: 0 var(--spacing-2);
  }
  
  .app-title {
    font-size: var(--font-size-base);
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .app-title .title-icon {
    font-size: 0.9rem;
  }
  
  .header-controls {
    flex-shrink: 1;
    justify-content: flex-end;
    gap: var(--spacing-1);
    flex-wrap: wrap;
    min-width: 0;
  }
  
  .persistent-email {
    display: none; /* 在移动端隐藏邮箱输入 */
  }
  
  .user-info {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-1);
    flex-wrap: wrap;
  }
  
  .user-info span {
    font-size: var(--font-size-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
  }
  
  .user-info .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1);
    min-width: auto;
    white-space: nowrap;
  }
  
  .theme-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    flex-shrink: 0;
  }
  
  .language-select {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1);
    min-width: 80px;
  }
  
  #themeToggle {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1);
    min-width: 32px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-1);
  }
  
  .app-title {
    font-size: var(--font-size-base);
    text-align: center;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-1);
  }
  
  .persistent-email,
  .user-info {
    order: 1;
  }
  
  .theme-toggle {
    order: 2;
    justify-content: center;
  }
  
  .user-info .btn {
    width: 100%;
    text-align: center;
  }
}

/* 持久化邮箱输入 */
.persistent-email {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-right: var(--spacing-4);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.persistent-email label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
}

.persistent-email input {
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-1);
  min-width: 200px;
  outline: none;
}

.persistent-email input:focus {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.persistent-email input.valid {
  color: var(--color-success);
}

.persistent-email input.invalid {
  color: var(--color-error);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少用户信息间距 */
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-3) var(--spacing-4);
  width: 100%;
  height: calc(100vh - var(--header-height, 80px));
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 登录面板 */
.login-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-3); /* 进一步减少登录卡片padding */
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-color);
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-2); /* 进一步减少标题下方间距 */
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.login-actions .btn {
  width: 100%;
}

.login-actions .btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

/* 工作区 - 动态高度适配 */
.workspace {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  flex: 1;
  overflow-x: hidden; /* 禁止水平滚动 */
  overflow-y: visible; /* 允许垂直滚动 */
  height: auto; /* 改为auto，由内容驱动高度 */
  min-height: 100%; /* 确保至少占满容器 */
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

/* 严格布局约束的左右列布局容器 - 修复竖屏显示问题 */
/* 旧的双列布局样式 - 已弃用，保留以防兼容性问题 */
/*
.grid-container-new {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: minmax(0, 1fr) auto;
  gap: var(--spacing-2);
  min-height: calc(100vh - 200px);
  height: auto;
  max-height: none;
  margin-bottom: var(--spacing-2);
  padding: var(--spacing-1);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  position: relative;
  overflow: visible;
}
*/

/* 旧的左右列样式 - 已被三列布局替代，保留以防兼容性问题 */
/*
.column-left {
  display: grid;
  grid-template-rows:
    minmax(150px, auto)
    minmax(180px, auto)
    minmax(120px, auto)
    minmax(100px, auto);
  gap: 0;
  min-height: 0;
  align-content: start;
}

.column-right {
  display: grid;
  grid-template-rows:
    minmax(160px, auto)
    minmax(180px, auto)
    minmax(220px, auto);
*/

/* 操作按钮区域跨越两列 */
.grid-span-full {
  grid-column: 1 / -1; /* 跨越所有列 */
  grid-row: 2; /* 位于第二行 */
}

/* 多选下拉菜单组件 - Fluent Design风格 */
.multi-select-dropdown {
  position: relative;
  width: 100%;
  min-height: 44px; /* 与其他表单元素保持一致 */
  display: flex;
  flex-direction: column;
}

.multi-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-height: 44px; /* 与其他表单元素保持一致 */
  box-shadow: var(--neu-shadow-inset);
  user-select: none;
  -webkit-user-select: none;
  flex-wrap: nowrap; /* 强制不换行 */
  white-space: nowrap; /* 防止内容换行 */
  width: 100%;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.multi-select-trigger:hover {
  box-shadow: var(--neu-shadow-subtle);
}

.multi-select-trigger:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.multi-select-trigger[aria-expanded="true"] {
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.multi-select-trigger[aria-expanded="true"] .multi-select-arrow {
  transform: rotate(180deg);
}

.multi-select-text {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.multi-select-text.placeholder {
  color: var(--text-secondary);
}

.multi-select-arrow {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  transition: transform var(--transition-normal);
  margin-left: var(--spacing-2);
}

.multi-select-options {
  position: fixed; /* 改为fixed定位，实现真正的浮窗效果 */
  top: auto; /* 将通过JavaScript动态设置 */
  left: auto; /* 将通过JavaScript动态设置 */
  width: auto; /* 将通过JavaScript动态设置 */
  background: var(--bg-tertiary);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-md); /* 完整圆角 */
  box-shadow:
    var(--neu-shadow-outset),
    0 8px 32px rgba(0, 0, 0, 0.3), /* 增强阴影效果 */
    0 4px 16px rgba(247, 92, 244, 0.2); /* 紫色光晕 */
  max-height: 350px; /* 增加最大高度以显示更多选项 */
  overflow-y: auto;
  z-index: 9999; /* 极高的z-index确保在最上层 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95); /* 添加缩放效果 */
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px); /* 毛玻璃背景 */
  -webkit-backdrop-filter: blur(10px);
}

.multi-select-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1); /* 完整显示状态 */
}

.multi-select-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-3);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  min-height: 44px; /* 与其他表单元素保持一致的触摸友好高度 */
  border-bottom: 1px solid var(--border-color);
  -webkit-user-select: none;
  user-select: none;
}

.multi-select-option:last-child {
  border-bottom: none;
}

.multi-select-option:hover {
  background: var(--bg-secondary);
}

.multi-select-option:focus {
  outline: none;
  background: var(--bg-secondary);
  box-shadow: inset 2px 0 0 var(--color-primary);
}

.multi-select-option:active {
  background: var(--color-gray-200);
}

.multi-select-checkbox {
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  max-width: 16px;
  max-height: 16px;
  margin-right: var(--spacing-2);
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 3px;
  position: relative;
  flex-shrink: 0;
  flex-grow: 0;
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.multi-select-checkbox:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px rgba(247, 92, 244, 0.2);
}

.multi-select-checkbox:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(247, 92, 244, 0.3);
}

.multi-select-checkbox:checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.multi-select-checkbox:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multi-select-label {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
  line-height: 1.4;
  text-align: left;
}

/* 隐藏原始select元素，保持表单兼容性 */
.multi-select-dropdown select {
  display: none !important;
}

/* 板块样式 - 内容自动展开优化 */
.panel {
  background: var(--bg-tertiary);
  border: none;
  box-shadow: var(--neu-shadow-outset);
  display: flex;
  flex-direction: column;
  overflow: visible;
  position: relative;
  transition: box-shadow var(--transition-normal);
  /* 移除固定最小高度，改为动态计算 */
  min-height: var(--panel-min-height, auto);
  height: auto; /* 允许内容驱动高度 */
}

/* 板块边缘贴合效果 */
.panel:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.panel:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.panel:first-child {
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.panel:last-child {
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.panel:only-child {
  border-radius: var(--radius-lg);
}

/* 板块悬停效果 */
.panel:hover {
  box-shadow: var(--neu-shadow-hover);
}

/* 键盘焦点效果 */
.panel.keyboard-focused {
  box-shadow: var(--neu-shadow-hover), 0 0 0 3px rgba(247, 92, 244, 0.3);
  outline: none;
}

.panel.keyboard-focused::before {
  content: '⌨️ 使用 Ctrl+↑/↓ 调整高度';
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: 1000;
  opacity: 0.9;
  pointer-events: none;
}

/* 板块内容区域 - 内容自动展开优化 */
.panel-content {
  flex: 1;
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  overflow: auto; /* 在内容过多时显示滚动条 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1); /* 从spacing-2减少到spacing-1 */
  min-height: 0;
  max-height: 500px; /* 设置合理的最大高度，防止过度扩展 */
  /* 确保内容完全可见 */
  width: 100%;
  box-sizing: border-box;
}

/* 板块拖拽手柄 - 触屏优化 */
.panel .resize-handle-bottom {
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 8px; /* 增加高度以改善触摸体验 */
  cursor: ns-resize;
  background: linear-gradient(90deg, transparent 20%, var(--color-primary) 50%, transparent 80%);
  opacity: 0;
  transition: all var(--transition-normal);
  z-index: 10;
  border-radius: var(--radius-sm);
  /* 触屏优化 */
  touch-action: pan-y; /* 只允许垂直拖拽 */
  -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
}

/* 触屏设备上的拖拽手柄 */
@media (pointer: coarse) {
  .panel .resize-handle-bottom {
    height: 12px; /* 触屏设备上更大的触摸目标 */
    opacity: 0.3; /* 在触屏设备上默认可见 */
  }

  .panel:hover .resize-handle-bottom,
  .panel .resize-handle-bottom:active {
    opacity: 0.8;
  }
}

.panel:hover .resize-handle-bottom {
  opacity: 0.6;
}

.panel .resize-handle-bottom:hover {
  opacity: 1 !important;
  background: var(--color-primary-hover);
  transform: scaleY(1.5);
}

.panel .resize-handle-bottom::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 2px;
  background: var(--color-white);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.panel .resize-handle-bottom:hover::before {
  opacity: 0.9;
}

/* 最后一个板块不显示拖拽手柄 */
.panel:last-child .resize-handle-bottom {
  display: none;
}

/* 田字格项目 - Neumorphism效果 */
.grid-item {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--neu-shadow-outset);
  display: flex;
  flex-direction: column;
  overflow: visible; /* 改为visible以防止内容被裁切 */
  position: relative;
  transition: box-shadow var(--transition-normal);
  /* 移除resize属性，改用自定义拖拽系统 */
  min-width: var(--min-col-width, 280px);  /* 使用CSS变量 */
  min-height: var(--min-row-height, 200px); /* 使用CSS变量 */
  max-width: calc(100vw - 40px);  /* 防止超出视口 */
  max-height: calc(100vh - 200px); /* 防止超出视口 */
}

/* 网格区域指定 */
.grid-item[data-grid-area="input"] {
  grid-area: input;
}

.grid-item[data-grid-area="basic"] {
  grid-area: basic;
}

.grid-item[data-grid-area="trip"] {
  grid-area: trip;
}

.grid-item[data-grid-area="config"] {
  grid-area: config;
}

/* 田字格项目悬停效果 */
.grid-item:hover {
  box-shadow: var(--neu-shadow-hover);
}

/* 田字格拖拽手柄 - 增强版 */
.grid-item {
  position: relative;
}

.resize-handle {
  position: absolute;
  background: var(--color-primary);
  opacity: 0;
  transition: all var(--transition-normal);
  z-index: 10;
  border-radius: var(--radius-sm);
}

.grid-item:hover .resize-handle {
  opacity: 0.6;
}

.resize-handle:hover {
  opacity: 1 !important;
  background: var(--color-primary-hover);
  transform: scale(1.1);
}

.resize-handle:active {
  background: var(--color-primary);
  transform: scale(1.2);
  box-shadow: 0 0 0 2px rgba(247, 92, 244, 0.3);
}

/* 右边缘拖拽手柄 - 禁用列宽调整 */
.resize-handle-right {
  display: none; /* 暂时禁用列宽调整以解决独立性问题 */
}

/* 底边缘拖拽手柄 - 调整行高 */
.resize-handle-bottom {
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 6px;
  cursor: ns-resize;
  data-resize-type: "row";
  /* 增强视觉指示 */
  background: linear-gradient(90deg, transparent 20%, var(--color-primary) 50%, transparent 80%);
}

.resize-handle-bottom::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 2px;
  background: var(--color-white);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle-bottom:hover::before {
  opacity: 0.9;
}

/* 右下角拖拽手柄 - 只调整行高 */
.resize-handle-corner {
  bottom: -3px;
  right: -3px;
  width: 12px;
  height: 12px;
  cursor: ns-resize; /* 改为只支持垂直调整 */
  border-radius: 50%;
  data-resize-type: "both";
  /* 增强视觉指示 */
  background: radial-gradient(circle, var(--color-primary) 30%, transparent 70%);
}

.resize-handle-corner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: var(--color-white);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle-corner:hover::before {
  opacity: 1;
}

/* 拖拽时的视觉反馈 */
.grid-item.resizing {
  box-shadow: var(--neu-shadow-pressed), 0 0 0 2px var(--color-primary);
  z-index: 20;
  transition: none; /* 拖拽时禁用过渡动画 */
}

.grid-container.resizing {
  user-select: none;
  -webkit-user-select: none;
  cursor: grabbing;
}

/* 网格调整过渡动画 */
.grid-container:not(.resizing) {
  transition: grid-template-columns 0.3s ease-out, grid-template-rows 0.3s ease-out;
}

/* 拖拽手柄增强视觉效果 */
.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 2px;
  background: var(--color-white);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle:hover::before {
  opacity: 0.8;
}

/* 网格线指示器（调整时显示） */
.grid-container.resizing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(to right, transparent calc(var(--grid-col-1, 50%) - 1px), var(--color-primary) calc(var(--grid-col-1, 50%) - 1px), var(--color-primary) calc(var(--grid-col-1, 50%) + 1px), transparent calc(var(--grid-col-1, 50%) + 1px)),
    linear-gradient(to bottom, transparent calc(var(--grid-row-1, 50%) - 1px), var(--color-primary) calc(var(--grid-row-1, 50%) - 1px), var(--color-primary) calc(var(--grid-row-1, 50%) + 1px), transparent calc(var(--grid-row-1, 50%) + 1px));
  opacity: 0.3;
  pointer-events: none;
  z-index: 5;
}

/* 田字格项目头部 */
.grid-item .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.grid-item .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 田字格项目内容 - 改善内容保护 */
.grid-item .input-card,
.grid-item .form-card {
  flex: 1;
  padding: var(--spacing-3);
  overflow: auto; /* 改为auto，允许滚动以防止内容被裁切 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-height: 0; /* 允许flex子项收缩 */
  /* 添加内容保护 */
  max-height: calc(100% - var(--spacing-6)); /* 确保不超出容器 */
}

/* 输入卡片特殊样式优化 */
.input-card {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  position: relative;
}

.input-card .form-group {
  margin-bottom: var(--spacing-2);
}

.input-card .form-group:last-child {
  margin-bottom: 0;
}

/* 输入卡片内的标签样式 */
.input-card label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
  display: block;
}

/* 订单输入区域特殊样式 */
.input-section textarea {
  min-height: 100px;
  resize: vertical;
}

/* Textarea容器 - 支持内嵌按钮 */
.textarea-container {
  position: relative;
  display: flex;
  flex-direction: column;
}

.textarea-container textarea {
  padding-right: 45px; /* 为内嵌按钮留出空间 */
  resize: vertical;
  min-height: 100px;
}

/* Textarea内嵌上传按钮 */
.textarea-upload-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--neu-shadow-subtle);
  z-index: 10;
}

.textarea-upload-button:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--neu-shadow-outset);
}

.textarea-upload-button:active {
  transform: translateY(0);
  box-shadow: var(--neu-shadow-inset);
}

.textarea-upload-button .upload-icon {
  font-size: 0.9rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.input-section .input-actions {
  margin-top: 0;
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--border-color);
}

/* 服务配置区域 */
.service-config-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacing-4);
}

.service-config-section .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.service-config-section .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.service-config-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

.config-left,
.config-right {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* ========================================
   服务配置价格样式重构 - 现代化设计
   ======================================== */

/* 紧凑价格输入组 - 与其他字段保持一致 */
.compact-price-input {
  display: flex;
  align-items: stretch;
  gap: 2px;
  background: transparent;
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
  width: 100%;
  min-height: var(--input-height-compact, 20px);
}

.compact-price-input input {
  flex: 1;
  width: 70%;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-right: 0;
}

.compact-price-input select {
  flex: 0 0 30%;
  width: 30%;
  padding: var(--spacing-3) var(--spacing-2);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 1px solid var(--border-color);
  text-align: center;
  cursor: pointer;
}

.compact-price-input input:focus,
.compact-price-input select:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.compact-price-input input:hover:not(:focus),
.compact-price-input select:hover:not(:focus) {
  box-shadow: var(--neu-shadow-subtle);
}

/* 紧凑价格输入响应式 */
@media (max-width: 768px) {
  .compact-price-input {
    gap: 1px;
    min-height: 18px;
  }
  
  .compact-price-input input,
  .compact-price-input select {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .compact-price-input input {
    width: 65%;
    flex: 1;
  }
  
  .compact-price-input select {
    width: 35%;
    flex: 0 0 35%;
    font-size: 10px;
    padding: var(--spacing-1) var(--spacing-2);
  }
}

/* 价格输入组 - 重构版本 */
.price-input-group {
  display: flex;
  align-items: stretch;
  gap: 0;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  position: relative;
  min-width: 200px; /* 确保最小宽度 */
  flex-wrap: nowrap; /* 强制不换行 */
  white-space: nowrap; /* 防止内容换行 */
}

.price-input-group:hover {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 1px rgba(247, 92, 244, 0.3);
}

.price-input-group:focus-within {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 
              0 0 0 3px rgba(247, 92, 244, 0.2),
              0 4px 20px rgba(247, 92, 244, 0.1); /* 增加阴影深度 */
}

/* 价格输入框 */
.price-input-group input {
  flex: 1; /* 自动占用剩余空间（约90%） */
  width: 90%; /* 明确设置宽度为90% */
  height: 48px;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-xl); /* 增大字体大小 */
  font-weight: 700; /* 增加字体粗细 */
  outline: none;
  transition: all var(--transition-normal);
  min-width: 0; /* 确保能够缩小 */
}

.price-input-group input:focus {
  outline: none;
  color: var(--color-primary);
  font-weight: 800; /* 聚焦时更加突出 */
}

/* 占位符样式优化 */
.price-input-group input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
  font-size: var(--font-size-lg); /* 占位符字体稍大 */
  opacity: 0.7;
}

/* 货币选择器 */
.price-input-group select {
  width: 10%; /* 固定为10%宽度 */
  min-width: 50px; /* 设置最小宽度确保可用性 */
  max-width: 60px; /* 设置最大宽度避免过大 */
  height: 48px;
  padding: var(--spacing-1) 2px; /* 进一步减少内边距 */
  border: none;
  border-left: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: var(--font-size-xs); /* 使用更小的字体 */
  font-weight: 500;
  outline: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  flex-shrink: 0;
  text-align: center; /* 居中显示货币代码 */
}

.price-input-group select:hover {
  background: var(--color-gray-100);
  color: var(--text-primary);
  transform: scale(1.02); /* 轻微放大效果 */
}

.price-input-group select:focus {
  outline: none;
  background: var(--color-primary);
  color: white;
  border-left-color: var(--color-primary);
}

/* 价格输入组增强效果 */
.price-input-group::before {
  content: '💰';
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-lg);
  opacity: 0.6;
  pointer-events: none;
  transition: all var(--transition-normal);
}

.price-input-group:focus-within::before {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.price-input-group input {
  padding-left: calc(var(--spacing-4) + 30px); /* 为图标留出空间 */
}

/* 价格验证状态 */
.price-input-group.valid {
  border-color: var(--color-success);
  box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.price-input-group.valid::after {
  content: '✓';
  position: absolute;
  right: calc(10% + 10px); /* 根据货币选择器10%宽度调整位置 */
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-success);
  font-weight: bold;
  font-size: var(--font-size-lg);
}

.price-input-group.invalid {
  border-color: var(--color-error);
  box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.price-input-group.invalid::after {
  content: '⚠️';
  position: absolute;
  right: calc(10% + 10px); /* 根据货币选择器10%宽度调整位置 */
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-lg);
}

/* 价格转换显示 */
.price-conversion-display {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: linear-gradient(135deg, rgba(247, 92, 244, 0.1), rgba(224, 64, 251, 0.05));
  border: 1px solid rgba(247, 92, 244, 0.2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all var(--transition-normal);
}

.price-conversion-display .conversion-icon {
  color: var(--color-primary);
  opacity: 0.8;
}

.price-conversion-display .original-price {
  font-weight: 500;
  color: var(--text-primary);
}

.price-conversion-display .conversion-arrow {
  color: var(--color-primary);
  font-weight: 600;
  margin: 0 var(--spacing-1);
}

.price-conversion-display .conversion-text {
  color: var(--text-secondary);
}

.price-conversion-display .conversion-rate {
  font-weight: 600;
  color: var(--color-primary);
}

/* 响应式调整 - 始终保持同一行 */
@media (max-width: 768px) {
  .price-input-group {
    flex-direction: row; /* 保持水平排列，不换行 */
    height: 48px; /* 固定高度 */
    min-width: 180px; /* 移动端最小宽度 */
  }
  
  .price-input-group input {
    border-radius: var(--radius-md) 0 0 var(--radius-md); /* 左侧圆角 */
    font-size: var(--font-size-lg); /* 在移动端稍微减小字体 */
    padding: var(--spacing-3) var(--spacing-2); /* 调整移动端内边距 */
    width: 90%; /* 确保90%宽度 */
  }
  
  .price-input-group select {
    width: 10%; /* 保持10%宽度 */
    min-width: 45px; /* 移动端最小宽度稍微减少 */
    max-width: 55px; /* 移动端最大宽度 */
    border-left: 1px solid var(--border-color); /* 保持左边框 */
    border-top: none; /* 移除顶部边框 */
    border-radius: 0 var(--radius-md) var(--radius-md) 0; /* 右侧圆角 */
    font-size: var(--font-size-xs); /* 移动端货币选择器字体 */
    padding: var(--spacing-1) 1px; /* 移动端减少内边距 */
    text-align: center; /* 居中显示货币代码 */
  }
}

/* 强制同一行显示 - 防止换行 */
.price-input-group {
  /* ...existing code... */
  flex-wrap: nowrap; /* 强制不换行 */
  white-space: nowrap; /* 防止内容换行 */
}

/* 确保输入框和选择器不会脱离flex容器 */
.price-input-group input,
.price-input-group select {
  flex-shrink: 1; /* 允许缩小以适应容器 */
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

/* 极小屏幕适配 - 保持同一行 */
@media (max-width: 480px) {
  .price-input-group {
    min-width: 150px; /* 极小屏幕最小宽度 */
  }
  
  .price-input-group input {
    font-size: var(--font-size-base); /* 极小屏幕字体 */
    padding: var(--spacing-2) var(--spacing-1); /* 减少内边距 */
  }
  
  .price-input-group select {
    min-width: 40px; /* 极小屏幕最小宽度 */
    max-width: 45px; /* 极小屏幕最大宽度 */
    padding: var(--spacing-1) 0; /* 极小内边距 */
    font-size: 10px; /* 固定小字体 */
  }
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* 操作按钮区域 - 跨列布局优化 */
.action-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-3); /* 减少内边距以适应grid */
  margin-top: var(--spacing-2); /* 与上方内容的间距 */
  margin-bottom: 0; /* 移除底部边距 */
}

.action-section .form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin: 0;
  padding: 0;
  border: none;
}

.action-section .btn {
  min-width: 150px;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 600;
}

/* 数据异常提示样式 */
.data-issues {
  color: var(--text-primary);
}

.issue-item {
  color: var(--color-warning);
  margin: var(--spacing-1) 0;
}

.issue-note {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-3);
  font-style: italic;
}

/* 区域样式 */
.input-section,
.preview-section,
.console-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 紧凑卡片的标题区域 */
.compact-card .section-header {
  padding: var(--spacing-1) var(--spacing-2); /* 进一步减少垂直间距 */
  background: transparent; /* 移除背景色 */
  border-bottom: none; /* 移除底部边框 */
  margin-bottom: var(--spacing-1); /* 减少与内容的间距 */
}

.section-header h3 {
  font-size: var(--font-size-base); /* 减少区域标题大小 */
  font-weight: 400;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 紧凑卡片的标题文字 */
.compact-card .section-header h3 {
  font-size: var(--font-size-sm); /* 进一步减少字体大小 */
  font-weight: 600; /* 增加字重保持可读性 */
  margin: 0; /* 移除默认边距 */
  line-height: 1.2; /* 紧凑行高 */
  white-space: nowrap; /* 防止换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移动端标题优化 */
@media (max-width: 768px) {
  .section-header h3 {
    font-size: var(--font-size-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }
  
  .compact-card .section-header h3 {
    font-size: var(--font-size-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
  }
  
  .section-controls {
    gap: var(--spacing-1);
  }
  
  .section-controls .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1);
    min-width: auto;
  }
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.input-card,
.preview-card,
.console-card {
  padding: var(--spacing-2); /* 从spacing-4减少到spacing-2 */
}

/* 实时分析相关样式 */
.realtime-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.realtime-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  width: fit-content;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: var(--spacing-3); /* 减少输入操作间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.image-upload-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.image-upload-controls .image-upload-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);  /* 减少圆角 */
  padding: var(--spacing-1) var(--spacing-2);  /* 大幅减少内边距 */
  font-size: var(--font-size-xs);  /* 减少字体大小 */
  font-weight: 500;  /* 减少字重 */
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);  /* 减少图标与文字间距 */
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);  /* 减少阴影 */
  position: relative;
  overflow: hidden;
  max-width: 100px;  /* 限制最大宽度 */
  height: 28px;      /* 固定高度 */
}

.image-upload-controls .image-upload-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.image-upload-controls .image-upload-button:hover::before {
  left: 100%;
}

.image-upload-controls .image-upload-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.image-upload-controls .upload-icon {
  font-size: 0.8rem;  /* 减少图标尺寸 */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 表单样式 - 大幅紧凑化 */
.form-group {
  margin-bottom: var(--spacing-1); /* 从spacing-2减少到spacing-1 */
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-1); /* 减少标签下方间距 */
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  min-height: 44px; /* 确保触摸友好 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.form-group input:hover:not(:focus),
.form-group select:hover:not(:focus),
.form-group textarea:hover:not(:focus) {
  box-shadow: var(--neu-shadow-subtle);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(244, 67, 54, 0.2);
}

.field-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px; /* 减少文本域最小高度 */
}

.form-section {
  margin-bottom: var(--spacing-4); /* 从spacing-8大幅减少到spacing-4 */
  padding-bottom: var(--spacing-3); /* 从spacing-6减少到spacing-3 */
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  font-size: var(--font-size-base); /* 减少表单区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-3); /* 减少标题下方间距 */
  padding-bottom: var(--spacing-1);
  border-bottom: 2px solid var(--color-primary);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为三列固定布局 */
  gap: var(--spacing-2); /* 进一步减少网格间距 */
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  cursor: pointer;
}

.checkbox-label:hover {
  color: var(--text-primary);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  -webkit-user-select: none; /* Safari, Chrome, Opera */
  -moz-user-select: none;    /* Firefox */
  -ms-user-select: none;     /* Internet Explorer/Edge */
  user-select: none;         /* Non-prefixed version, currently supported by Chrome, Opera and Firefox */
}

.btn:hover:not(:disabled) {
  box-shadow: var(--neu-shadow-hover);
  transform: translateY(-1px);
}

.btn:active:not(:disabled) {
  box-shadow: var(--neu-shadow-pressed);
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: var(--neu-shadow-subtle);
}

.btn-primary {
  background: var(--color-primary-gradient);
  color: var(--color-white);
  box-shadow: var(--neu-shadow-outset), 0 4px 15px rgba(247, 92, 244, 0.3);
}

.btn-primary:hover:not(:disabled) {
  box-shadow: var(--neu-shadow-hover), 0 8px 25px rgba(247, 92, 244, 0.4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2); /* 减少小按钮padding */
  font-size: var(--font-size-xs);
}

.btn-icon {
  width: 32px;  /* 减少图标按钮大小 */
  height: 32px;
  padding: 0;
  border-radius: 50%;
}

/* 主题切换区域 */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;  /* 防止收缩 */
  min-width: 0;    /* 允许内部元素收缩 */
}

/* 语言选择下拉菜单 - Neumorphism风格 */
.language-select {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  min-width: 70px;  /* 减少最小宽度 */
  min-height: 44px; /* 确保触摸友好 */
  margin-right: var(--spacing-2);
  flex-shrink: 0;   /* 防止收缩 */
}

.language-select:hover:not(:focus) {
  box-shadow: var(--neu-shadow-subtle);
}

.language-select:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.language-select option {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--spacing-2);
}

.loading-spinner {
  font-size: var(--font-size-base);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 操作按钮组 */
.form-actions {
  display: flex;
  gap: var(--spacing-2); /* 减少按钮组间距 */
  margin-top: var(--spacing-4); /* 减少顶部间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 日志控制台 */
.log-console {
  background: var(--color-gray-900);
  color: var(--color-gray-100);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  padding: var(--spacing-3); /* 减少控制台padding */
  border-radius: var(--radius-md);
  height: 250px; /* 减少控制台高度 */
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-entry {
  margin-bottom: var(--spacing-1); /* 减少日志条目间距 */
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  border-left: 3px solid transparent;
}

.log-entry.info {
  background: rgba(6, 182, 212, 0.1);
  border-left-color: var(--color-info);
}

.log-entry.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
}

.log-entry.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
}

.log-entry.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
}

.log-timestamp {
  color: var(--color-gray-400);
  font-size: var(--font-size-xs);
}

/* 切换开关 */
.toggle-switch {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  width: 40px; /* 减少开关大小 */
  height: 20px;
  background: var(--color-gray-300);
  border-radius: 10px;
  position: relative;
  transition: background-color var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px; /* 减少开关按钮大小 */
  height: 16px;
  border-radius: 50%;
  background: var(--color-white);
  top: 2px;
  left: 2px;
  transition: transform var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

/* 状态栏 */
.status-bar {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-1) var(--spacing-4); /* 减少状态栏padding */
  font-size: var(--font-size-xs);
}

.status-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: var(--spacing-3);
}

.status-item {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 模态框 - 重点优化，确保订单预览无滚动显示 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000; /* 提高z-index确保在预览浮窗之上 */
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: var(--spacing-4); /* 添加模态框外边距 */
}

.modal-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 900px; /* 增加最大宽度 */
  max-height: 90vh; /* 减少最大高度，确保有边距 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h3 {
  font-size: var(--font-size-base); /* 减少模态框标题大小 */
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-3); /* 大幅减少模态框body padding */
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-footer {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框footer padding */
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2); /* 减少按钮间距 */
  flex-shrink: 0;
}

/* 错误弹窗专用样式 */
.error-modal-content {
  max-width: 600px;
  width: 100%;
}

.error-main-message {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-md);
}

.error-main-message h4 {
  color: #dc2626;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--spacing-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-main-message p {
  color: var(--text-primary);
  margin: 0;
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.error-validation-details {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: var(--radius-md);
}

.error-validation-details h4 {
  color: #d97706;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--spacing-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.error-list li {
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid rgba(245, 158, 11, 0.2);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.error-list li:last-child {
  border-bottom: none;
}

.error-list li strong {
  color: #d97706;
  font-weight: 600;
  text-transform: capitalize;
}

.error-code {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: var(--radius-md);
}

.error-code h4 {
  color: #6b7280;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--spacing-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-code p {
  color: var(--text-secondary);
  margin: 0;
  font-size: var(--font-size-sm);
  font-family: 'Courier New', monospace;
}

.error-suggestions {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: var(--radius-md);
}

.error-suggestions h4 {
  color: #2563eb;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--spacing-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-suggestions p {
  color: var(--text-primary);
  margin: 0;
  font-size: var(--font-size-base);
  line-height: 1.5;
}

/* 订单预览专用样式 - 极致紧凑化 */
.order-preview {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
}

.order-preview h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2);
  color: var(--color-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-1);
}

.order-preview h5 {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-weight: 600;
}

.order-preview p {
  margin: 0 0 var(--spacing-1) 0; /* 极致减少段落间距 */
  line-height: var(--line-height-tight);
}

/* 历史订单面板 - 全屏模态框样式 */
.history-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  padding: var(--spacing-4);
}

.history-panel.hidden {
  display: none;
}

.history-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.history-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.history-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.history-controls {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.history-search {
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.search-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.history-stats {
  padding: var(--spacing-3);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-2);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--neu-shadow-inset);
}

.stat-label {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--color-primary);
}

.history-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: var(--spacing-3);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-weight: bold;
  color: var(--text-primary);
}

.list-count {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-2);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 多订单面板 - 浮窗样式 */
.multi-order-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  max-height: 800px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-xl);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 
              0 8px 25px rgba(0, 0, 0, 0.1),
              var(--neu-shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  animation: multiOrderPanelShow 0.3s ease-out;
  /* 确保面板初始居中状态 */
  margin: 0;
}

/* 全局隐藏类 */
.hidden {
  display: none !important;
}

.multi-order-panel.hidden {
  display: none !important;
}

.multi-order-panel {
  display: none; /* 默认隐藏 */
}

.multi-order-overlay {
  /* 移除这个包装层，直接在panel上设置样式 */
  display: none;
}

.multi-order-content {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--neu-shadow-outset);
}

/* 浮窗显示动画 */
@keyframes multiOrderPanelShow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 浮窗隐藏动画 */
@keyframes multiOrderPanelHide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

.multi-order-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.multi-order-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.multi-order-controls {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
}

.order-stats {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.order-count {
  font-weight: bold;
  color: var(--color-primary);
}

.date-range {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

/* 优化多订单面板头部按钮样式 */
.header-actions .btn {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* 批量创建按钮优化 */
.header-actions .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  font-size: 13px;
  border-radius: 8px;
  box-shadow: 
    0 2px 8px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: none;
  min-height: 36px;
}

.header-actions .btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 
    0 4px 16px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.header-actions .btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 
    0 2px 6px rgba(102, 126, 234, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图标按钮基础样式优化 */
.header-actions .btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.header-actions .btn-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.header-actions .btn-icon:hover:not(:disabled)::before {
  opacity: 0.7;
}

/* 最小化/最大化按钮样式 */
.header-actions .btn-toggle {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  box-shadow: 
    0 2px 8px rgba(79, 172, 254, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.header-actions .btn-toggle:hover:not(:disabled) {
  background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
  box-shadow: 
    0 4px 16px rgba(79, 172, 254, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px) scale(1.05);
}

.header-actions .btn-toggle:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
  box-shadow: 
    0 2px 6px rgba(79, 172, 254, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 关闭按钮样式 */
.header-actions .btn-close {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  box-shadow: 
    0 2px 8px rgba(255, 107, 107, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.header-actions .btn-close:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
  box-shadow: 
    0 4px 16px rgba(255, 107, 107, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px) scale(1.05);
}

.header-actions .btn-close:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
  box-shadow: 
    0 2px 6px rgba(255, 107, 107, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮焦点状态 */
.header-actions .btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 按钮禁用状态 */
.header-actions .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 按钮加载状态动画 */
.header-actions .btn.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.header-actions .btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: white;
}

/* 按钮成功状态 */
.header-actions .btn.success {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%) !important;
  color: white !important;
}

.header-actions .btn.success::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  font-weight: bold;
  animation: successPulse 0.6s ease-out;
}

/* 按钮错误状态 */
.header-actions .btn.error {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
  color: white !important;
  animation: shake 0.5s ease-in-out;
}

/* 动画定义 */
@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes successPulse {
  0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* 按钮悬停时的脉冲效果 */
.header-actions .btn-primary:hover:not(:disabled) {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
  50% { box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3); }
  100% { box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
}

.multi-order-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-2);
}

.multi-order-footer {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.batch-actions {
  display: flex;
  gap: var(--spacing-2);
}

.creation-summary {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.batch-create-status {
  padding: var(--spacing-2);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: none;
}

.batch-create-status.active {
  display: block;
}

/* 订单项样式 - 适配新的结构化显示 */
.order-item {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-3);
  box-shadow: var(--neu-shadow-sm);
  transition: all var(--transition-normal);
}

.order-item:hover {
  box-shadow: var(--neu-shadow-md);
  transform: translateY(-1px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.order-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.order-selector input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary);
}

.order-selector label {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  cursor: pointer;
}

.order-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: var(--radius-full);
  font-size: var(--font-size-small);
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.status-pending {
  background: var(--color-warning-light);
  color: var(--color-warning);
  border: 1px solid var(--color-warning);
}

.status-badge.status-parsed {
  background: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.status-badge.status-processing {
  background: var(--color-info-light);
  color: var(--color-info);
  border: 1px solid var(--color-info);
}

.order-content {
  padding: var(--spacing-3);
}

/* 订单摘要样式 */
.order-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.summary-item label {
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 50px;
  margin: 0;
}

.summary-item span {
  color: var(--text-primary);
  flex: 1;
}

/* 订单详细字段样式 */
.order-fields-container {
  margin-top: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.fields-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.fields-row:last-child {
  margin-bottom: 0;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.field-group label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  margin: 0;
}

.field-group input,
.field-group select,
.field-group textarea {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.field-group input:focus,
.field-group select:focus,
.field-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.price-group {
  display: flex;
  gap: var(--spacing-2);
}

.price-group input {
  flex: 2;
}

.price-group select {
  flex: 1;
  min-width: 80px;
}

.checkbox-group {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-weight: normal;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.order-actions .btn {
  min-width: 80px;
  font-size: var(--font-size-small);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-summary-grid {
    grid-template-columns: 1fr;
  }
  
  .fields-row {
    grid-template-columns: 1fr;
  }
  
  .checkbox-group {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .order-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .multi-order-content {
    max-height: 95vh;
    margin: var(--spacing-2);
  }
}

/* 历史订单状态样式 */
.history-item.success {
  border-left-color: var(--color-success); /* 成功状态绿色左边框 */
}

.history-item.failed {
  border-left-color: var(--color-error); /* 失败状态红色左边框 */
  background-color: #fff5f5; /* 轻微的红色背景 */
}

.history-item-status {
  font-weight: bold;
  font-size: var(--font-size-small);
  padding: 2px 8px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 8px;
}

.history-item.success .history-item-header {
  border-bottom-color: var(--color-success-light);
}

.history-item.failed .history-item-header {
  border-bottom-color: var(--color-error-light);
}

.history-item.failed .history-item-content p:last-child {
  background-color: var(--color-error-light);
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  font-size: var(--font-size-small);
}

/* ==============================================
   多订单模式移动端触屏优化 (v3.0)
   ============================================== */

/* 移动端基础变量 */
@media (max-width: 768px) {
  :root {
    /* 移动端专用字体大小 - 多订单模块字体放大30% */
    --font-size-mobile-xs: 0.845rem;   /* 13.52px - 原0.65rem + 30% */
    --font-size-mobile-sm: 0.91rem;    /* 14.56px - 原0.7rem + 30% */
    --font-size-mobile-base: 0.975rem; /* 15.6px - 原0.75rem + 30% */
    --font-size-mobile-lg: 1.04rem;    /* 16.64px - 原0.8rem + 30% */
    --font-size-mobile-xl: 1.17rem;    /* 18.72px - 原0.9rem + 30% */
    
    /* 多订单专用字体大小 - 进一步优化可读性 */
    --multi-order-font-xs: 0.91rem;    /* 14.56px */
    --multi-order-font-sm: 0.975rem;   /* 15.6px */
    --multi-order-font-base: 1.04rem;  /* 16.64px */
    --multi-order-font-lg: 1.17rem;    /* 18.72px */
    --multi-order-font-xl: 1.3rem;     /* 20.8px */
    
    /* 移动端触摸区域最小尺寸 */
    --touch-target-min: 44px;
    
    /* 移动端间距优化 - 适当增加以配合字体放大 */
    --mobile-spacing-xs: 0.3rem;      /* 4.8px - 增加20% */
    --mobile-spacing-sm: 0.5rem;      /* 8px - 增加25% */
    --mobile-spacing-md: 0.75rem;     /* 12px - 增加25% */
    --mobile-spacing-lg: 1rem;        /* 16px - 增加25% */
  }
}

/* 多订单面板移动端优化 - 改进字体和布局 */
@media (max-width: 768px) {
  .multi-order-panel {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    top: 0;
    left: 0;
    transform: none;
    border-radius: 0;
    /* 移动端全屏显示 */
  }

  .multi-order-content {
    border-radius: 0;
    height: 100vh;
  }

  .multi-order-header {
    padding: var(--mobile-spacing-lg); /* 使用更大的内边距 */
    flex-wrap: wrap;
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
  }

  .multi-order-header h3 {
    font-size: var(--multi-order-font-xl); /* 使用更大的字体 */
    flex: 1;
    min-width: 0;
  }

  .multi-order-controls {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
  }

  .order-stats {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
  }

  .order-count {
    font-size: var(--multi-order-font-lg); /* 使用更大的字体 */
    font-weight: 600;
  }

  .date-range {
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
  }

  .header-actions {
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
    flex-wrap: wrap; /* 允许按钮换行 */
  }

  .header-actions .btn {
    min-height: var(--touch-target-min); /* 确保触摸友好 */
    min-width: var(--touch-target-min); 
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    font-weight: 600;
    border-radius: 10px; /* 移动端使用更大的圆角 */
  }

  /* 移动端批量创建按钮优化 */
  .header-actions .btn-primary {
    padding: 10px 18px; /* 增加内边距 */
    font-size: 14px; /* 更大的字体 */
    min-height: 44px; /* iOS推荐的最小触摸区域 */
  }

  /* 移动端图标按钮优化 */
  .header-actions .btn-icon {
    width: 44px; /* iOS推荐的最小触摸区域 */
    height: 44px;
    font-size: 18px; /* 更大的图标 */
    border-radius: 10px; /* 更大的圆角 */
  }

  /* 移动端焦点状态优化 */
  .header-actions .btn:focus-visible {
    outline: 3px solid var(--color-primary);
    outline-offset: 3px;
  }
}

/* 订单卡片移动端优化 - 改进字体和布局 */
@media (max-width: 768px) {
  .order-item {
    margin-bottom: var(--mobile-spacing-lg); /* 使用更大的间距 */
    border-radius: var(--radius-lg); /* 使用更大的圆角 */
  }

  .order-header {
    padding: var(--mobile-spacing-lg); /* 使用更大的内边距 */
    flex-wrap: wrap;
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
  }

  .order-selector {
    flex: 1;
    min-width: 0;
  }

  .order-selector input[type="checkbox"] {
    width: 24px; /* 增加复选框大小以便触摸 */
    height: 24px;
  }

  .order-selector label {
    font-size: var(--multi-order-font-lg); /* 使用更大的字体 */
    font-weight: 600;
  }

  .order-status {
    align-self: flex-start;
  }

  .status-badge {
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    padding: var(--mobile-spacing-sm) var(--mobile-spacing-md); /* 使用更大的内边距 */
    font-weight: 600;
  }
}

/* 订单摘要移动端优化 - 三列布局增强可读性版本 */
@media (max-width: 768px) {
  .order-summary {
    padding: var(--mobile-spacing-md); /* 使用更大的内边距 */
    font-size: var(--multi-order-font-sm); /* 使用更大的字体 */
    line-height: 1.4;
  }

  .order-summary strong {
    font-size: var(--multi-order-font-base); /* 使用更大的强调字体 */
    font-weight: 700;
  }

  /* 订单摘要网格布局优化 - 三列布局 */
  .order-summary-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 三列布局 */
    gap: var(--mobile-spacing-sm); /* 适中的间隔 */
    margin-bottom: var(--mobile-spacing-md); /* 使用更大的底部间距 */
  }

  .order-summary-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: var(--mobile-spacing-sm); /* 使用适中的内边距 */
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--color-primary);
    min-height: 52px; /* 适中的最小高度 */
    position: relative;
  }

  .order-summary-label {
    font-weight: 700;
    color: var(--text-secondary);
    font-size: var(--multi-order-font-xs); /* 使用较小的标签字体 */
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: var(--mobile-spacing-xs);
    line-height: 1.2;
  }

  .order-summary-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--multi-order-font-sm); /* 使用适中的值字体 */
    word-break: break-word;
    width: 100%;
    line-height: 1.3;
  }

  /* 必填字段缺失的警告样式 */
  .order-summary-value.missing-field {
    color: var(--color-error);
    font-weight: 700;
    border: 2px dashed var(--color-error);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    border-radius: var(--radius-md);
    background: rgba(244, 67, 54, 0.08);
    font-size: var(--multi-order-font-sm);
  }

  .order-summary-value.missing-field::before {
    content: "⚠️ ";
    font-size: var(--multi-order-font-base);
  }

  /* OTA参考号警告特殊样式 */
  .ota-reference-warning {
    grid-column: 1 / -1; /* 跨所有列显示 */
  }

  .ota-reference-warning .order-summary-value {
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
    font-size: var(--multi-order-font-sm);
  }

  /* 整体警告提示 */
  .order-summary-warning {
    grid-column: 1 / -1; /* 跨所有列显示 */
    background: rgba(255, 152, 0, 0.12);
    border: 2px solid var(--color-warning);
    padding: var(--mobile-spacing-md);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
    margin-top: var(--mobile-spacing-md);
  }

  .warning-icon {
    color: var(--color-warning);
    font-size: var(--multi-order-font-lg);
  }

  .warning-text {
    color: var(--color-warning);
    font-size: var(--multi-order-font-sm);
    font-weight: 700;
    flex: 1;
  }
}

/* 订单操作按钮移动端优化 - 增强可读性版本 */
@media (max-width: 768px) {
  .order-actions {
    padding: var(--mobile-spacing-md); /* 使用更大的内边距 */
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
    flex-wrap: wrap;
    justify-content: center;
  }

  .order-actions .btn {
    min-height: var(--touch-target-min); /* 保持触摸友好高度 */
    padding: var(--mobile-spacing-md) var(--mobile-spacing-lg); /* 使用更大的内边距 */
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    font-weight: 600;
    flex: 1;
    min-width: calc(50% - var(--mobile-spacing-sm)); /* 两列布局，保留间距 */
    max-width: 160px; /* 增加最大宽度 */
    border-radius: var(--radius-md);
    
    /* 触摸反馈优化 */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .order-actions .btn:active {
    transform: scale(0.96);
    transition: transform 0.1s ease;
  }
}

/* 多订单底部操作区移动端优化 - 增强可读性版本 */
@media (max-width: 768px) {
  .multi-order-footer {
    padding: var(--mobile-spacing-lg); /* 使用更大的内边距 */
    flex-direction: column;
    gap: var(--mobile-spacing-lg); /* 使用更大的间隔 */
    align-items: stretch;
    background: var(--bg-secondary);
    border-top: 2px solid var(--border-color);
  }

  .batch-actions {
    justify-content: center;
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
    flex-wrap: wrap;
  }

  .batch-actions .btn {
    min-height: var(--touch-target-min); /* 保持触摸友好高度 */
    padding: var(--mobile-spacing-md) var(--mobile-spacing-lg); /* 使用更大的内边距 */
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    font-weight: 600;
    flex: 1;
    min-width: calc(33.33% - var(--mobile-spacing-sm)); /* 三列布局 */
    border-radius: var(--radius-md);
  }

  .creation-summary {
    justify-content: center;
    text-align: center;
    gap: var(--mobile-spacing-lg); /* 使用更大的间隔 */
    flex-direction: column;
    padding: var(--mobile-spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
  }

  .creation-summary span {
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    font-weight: 600;
    color: var(--text-primary);
  }

  #createSelectedOrdersBtn {
    min-height: calc(var(--touch-target-min) + 8px); /* 使按钮更大 */
    padding: var(--mobile-spacing-lg) var(--mobile-spacing-xl); /* 使用更大的内边距 */
    font-size: var(--multi-order-font-lg); /* 使用更大的字体 */
    font-weight: 700;
    border-radius: var(--radius-lg);
  }
}

/* 订单字段编辑区域移动端优化 - 增强可读性双列布局 */
@media (max-width: 768px) {
  .order-fields-grid {
    padding: var(--mobile-spacing-md); /* 使用更大的内边距 */
    display: grid !important;
    grid-template-columns: 1fr 1fr; /* 改为双列布局，提升可读性 */
    gap: var(--mobile-spacing-md); /* 使用更大的间隔 */
    font-size: var(--multi-order-font-sm); /* 使用更大的字体 */
    line-height: 1.4;
  }

  .order-field-group {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-spacing-sm); /* 使用更大的内部间距 */
    margin-bottom: var(--mobile-spacing-sm);
  }

  .order-field-label {
    font-size: var(--multi-order-font-sm); /* 使用更大的标签字体 */
    font-weight: 700;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: var(--mobile-spacing-xs);
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0;
  }

  .order-field-input {
    min-height: var(--touch-target-min); /* 确保触摸友好 */
    padding: var(--mobile-spacing-md); /* 使用更大的内边距 */
    font-size: var(--multi-order-font-base); /* 使用更大的字体 */
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    font-weight: 500;
  }

  .order-field-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(247, 92, 244, 0.2);
    background: var(--bg-primary);
  }

  /* 特殊字段处理 */
  .order-field-group.full-width {
    grid-column: 1 / -1; /* 跨两列 */
  }

  .order-field-group.price-group {
    grid-column: 1 / -1; /* 价格组跨两列 */
  }

  .price-input-group {
    display: flex;
    gap: var(--mobile-spacing-sm);
    align-items: stretch;
  }

  .price-input-group .price-input {
    flex: 3; /* 价格输入框占75% */
    font-size: var(--multi-order-font-base);
  }

  .price-input-group .currency-select {
    flex: 1; /* 货币选择占25% */
    font-size: var(--multi-order-font-sm);
    padding: var(--mobile-spacing-sm);
    min-width: 80px;
  }

  .textarea-input {
    min-height: calc(var(--touch-target-min) * 2) !important; /* 文本域最小高度 */
    resize: vertical;
    font-size: var(--multi-order-font-base);
    padding: var(--mobile-spacing-md);
    line-height: 1.4;
  }

  /* 复选框组优化 */
  .checkbox-wrapper {
    display: flex;
    gap: var(--mobile-spacing-md);
    flex-wrap: wrap;
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
    font-size: var(--multi-order-font-sm);
    white-space: nowrap;
    min-height: var(--touch-target-min);
  }

  .checkbox-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: var(--color-primary);
  }

  .checkbox-item span {
    font-size: var(--multi-order-font-sm);
    line-height: 1.3;
    font-weight: 500;
  }

  /* 必填字段视觉提示 */
  .order-field-group.required .order-field-label::after {
    content: " *";
    color: var(--color-error);
    font-weight: bold;
    font-size: var(--multi-order-font-base);
  }

  .order-field-group.required.invalid .order-field-input {
    border-color: var(--color-error);
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.15);
    background: rgba(244, 67, 54, 0.03);
  }

  .order-field-group.invalid {
    position: relative;
  }

  .order-field-group.invalid::after {
    content: "⚠️";
    position: absolute;
    right: var(--mobile-spacing-sm);
    top: calc(var(--multi-order-font-sm) + var(--mobile-spacing-xs) + var(--mobile-spacing-sm));
    z-index: 10;
    font-size: var(--multi-order-font-lg);
    color: var(--color-error);
  }
}

  /* OTA参考号特殊处理 */
  .ota-reference-group {
    position: relative;
    display: flex;
    align-items: center;
  }

  .ota-reference-missing {
    border: 1px dashed var(--color-warning) !important;
    background-color: rgba(255, 152, 0, 0.05);
    padding-right: 16px !important;
  }

  .ota-reference-generate-btn {
    position: absolute;
    right: 1px;
    top: 1px;
    bottom: 1px;
    width: 14px;
    border: none;
    background: var(--color-primary);
    color: white;
    border-radius: 1px;
    font-size: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .ota-reference-generate-btn:active {
    transform: scale(0.95);
    background: var(--color-primary-hover);
  }

  /* 验证反馈容器 */
  .field-validation-container {
    margin-top: 0.5px;
    min-height: 10px;
    font-size: 6px;
  }

  .field-validation-message,
  .field-success-message {
    font-size: 6px;
    padding: 0.5px 1px;
    border-radius: 1px;
    display: flex;
    align-items: center;
    gap: 0.5px;
  }

  .field-validation-message {
    color: var(--color-error);
    background: rgba(244, 67, 54, 0.05);
    border: 1px solid rgba(244, 67, 54, 0.2);
  }

  .field-success-message {
    color: var(--color-success);
    background: rgba(76, 175, 80, 0.05);
    border: 1px solid rgba(76, 175, 80, 0.2);
  }

  .field-validation-message::before {
    content: "⚠️";
    font-size: 6px;
  }

  .field-success-message::before {
    content: "✅";
    font-size: 6px;
  }
}

/* 快捷编辑功能样式 */
@media (max-width: 768px) {
  .quick-edit-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 3000;
    display: flex;
    align-items: flex-end;
    /* 从底部滑入 */
  }

  .quick-edit-panel {
    background: var(--color-white);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    padding: var(--mobile-spacing-lg);
    transform: translateY(100%);
    transition: transform var(--transition-normal);
  }

  .quick-edit-panel.show {
    transform: translateY(0);
  }

  .quick-edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--mobile-spacing-lg);
    padding-bottom: var(--mobile-spacing-md);
    border-bottom: 2px solid var(--border-color);
  }

  .quick-edit-title {
    font-size: var(--font-size-mobile-lg);
    font-weight: 600;
    color: var(--text-primary);
  }

  .quick-edit-close {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
    border: none;
    background: var(--bg-secondary);
    border-radius: 50%;
    font-size: var(--font-size-mobile-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* OTA参考号功能增强样式 */
@media (max-width: 768px) {
  .ota-reference-group {
    position: relative;
  }

  .ota-reference-missing {
    border: 2px dashed var(--color-warning) !important;
    background-color: rgba(255, 152, 0, 0.05);
  }

  .ota-reference-generate-btn {
    position: absolute;
    right: var(--mobile-spacing-xs);
    top: 50%;
    transform: translateY(-50%);
    min-height: 36px;
    min-width: 36px;
    border: none;
    background: var(--color-primary);
    color: white;
    border-radius: var(--radius-md);
    font-size: var(--font-size-mobile-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .ota-reference-generate-btn:active {
    transform: translateY(-50%) scale(0.95);
  }

  .ota-reference-hint {
    font-size: var(--font-size-mobile-xs);
    color: var(--color-warning);
    margin-top: var(--mobile-spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-xs);
  }

  .ota-reference-hint::before {
    content: "💡";
    font-size: var(--font-size-mobile-xs);
  }
}

/* 动画和交互增强 */
@media (max-width: 768px) {
  .order-item {
    transition: all var(--transition-normal);
  }

  .order-item.editing {
    box-shadow: 0 8px 32px rgba(247, 92, 244, 0.15);
    border-color: var(--color-primary);
  }

  .order-item.invalid {
    animation: shake 0.5s ease-in-out;
    border-color: var(--color-error);
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  .btn-mobile-optimized {
    position: relative;
    overflow: hidden;
  }

  .btn-mobile-optimized::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  .btn-mobile-optimized:active::before {
    width: 200px;
    height: 200px;
  }
}

/* 滚动优化 - 超紧凑布局 */
@media (max-width: 768px) {
  .multi-order-list {
    -webkit-overflow-scrolling: touch;
    /* iOS 平滑滚动 */
    scroll-behavior: smooth;
    padding: 2px; /* 极小内边距 */
  }

  .multi-order-list::-webkit-scrollbar {
    width: 2px; /* 更细的滚动条 */
  }

  .multi-order-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .multi-order-list::-webkit-scrollbar-thumb {
    background: var(--color-gray-300);
    border-radius: 1px;
  }
}

/* 横屏模式优化 - 超紧凑布局 */
@media (max-width: 768px) and (orientation: landscape) {
  .multi-order-header {
    padding: 2px 4px; /* 极小内边距 */
  }

  .multi-order-header h3 {
    font-size: 9px; /* 极小字体 */
  }

  .order-item {
    margin-bottom: 2px; /* 极小间距 */
  }

  .order-header,
  .order-summary,
  .order-actions {
    padding: 2px 4px; /* 极小内边距 */
  }

  .multi-order-footer {
    padding: 1px 2px; /* 极小内边距 */
    flex-direction: row;
    gap: 2px; /* 极小间隔 */
  }

  .creation-summary {
    flex-direction: row;
    gap: 2px; /* 极小间隔 */
  }

  .creation-summary span {
    font-size: var(--mobile-compact-xs); /* 保持极小字体 */
    margin-bottom: 0; /* 横屏时无底部间距 */
    margin-right: 2px; /* 右侧极小间距 */
  }

  #createSelectedOrdersBtn {
    height: var(--mobile-btn-height-xs); /* 横屏时更小 */
    min-width: 50px; /* 进一步减少宽度 */
    font-size: 8px; /* 横屏极小字体 */
  }

/* OTA参考号功能增强样式 - v4.0 超紧凑集成设计 */
@media (max-width: 768px) {
  .ota-reference-group {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .field-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .ota-reference-missing {
    border: 1px dashed var(--color-warning) !important;
    background-color: rgba(255, 152, 0, 0.02);
    padding-right: 20px !important; /* 为内置按钮预留最小空间 */
  }

  /* 内置生成按钮 - 集成到输入框内部右侧 */
  .ota-reference-generate-btn,
  .ota-quick-generate-btn {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    min-height: 16px;
    min-width: 16px;
    max-height: 16px;
    max-width: 16px;
    border: none;
    background: var(--color-primary);
    color: white;
    border-radius: 2px;
    font-size: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 15;
    transition: all var(--transition-fast);
    padding: 0;
    line-height: 1;
    opacity: 0.8;
  }

  /* 悬停效果 */
  .ota-reference-generate-btn:hover,
  .ota-quick-generate-btn:hover {
    opacity: 1;
    background: var(--color-primary-hover);
    transform: translateY(-50%) scale(1.1);
  }

  /* 点击效果 */
  .ota-reference-generate-btn:active,
  .ota-quick-generate-btn:active {
    transform: translateY(-50%) scale(0.9);
    background: var(--color-primary);
  }

  /* 输入框获得焦点时按钮显示增强 */
  .ota-reference-group .order-field-input:focus + .ota-reference-generate-btn,
  .ota-reference-group .order-field-input:focus + .ota-quick-generate-btn {
    opacity: 1;
    transform: translateY(-50%) scale(1.05);
  }
}

/* 字段验证反馈样式增强 */
@media (max-width: 768px) {
  .field-validation-container {
    margin-top: var(--mobile-spacing-xs);
    min-height: 16px;
  }

  .field-validation-message {
    font-size: var(--font-size-mobile-xs);
    color: var(--color-error);
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-xs);
    padding: var(--mobile-spacing-xs);
    background: rgba(244, 67, 54, 0.05);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(244, 67, 54, 0.2);
  }

  .field-validation-message::before {
    content: "⚠️";
    font-size: var(--font-size-mobile-xs);
  }

  .field-success-message {
    font-size: var(--font-size-mobile-xs);
    color: var(--color-success);
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-xs);
    padding: var(--mobile-spacing-xs);
    background: rgba(76, 175, 80, 0.05);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(76, 175, 80, 0.2);
  }

  .field-success-message::before {
    content: "✅";
    font-size: var(--font-size-mobile-xs);
  }

  /* 必填字段视觉指示器 */
  .order-field-group.required .order-field-label::after {
    content: " *";
    color: var(--color-error);
    font-weight: bold;
    font-size: var(--font-size-mobile-sm);
  }

  .order-field-group.required.invalid .order-field-input {
    border-color: var(--color-error);
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
    background: rgba(244, 67, 54, 0.02);
  }

  .order-field-group.invalid {
    position: relative;
  }

  .order-field-group.invalid::after {
    content: "⚠️";
    position: absolute;
    right: var(--mobile-spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    font-size: var(--font-size-mobile-base);
    color: var(--color-error);
  }
}

/* 快捷编辑功能样式 */
@media (max-width: 768px) {
  .quick-edit-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 3000;
    display: flex;
    align-items: flex-end;
    /* 从底部滑入 */
  }

  .quick-edit-panel {
    background: var(--color-white);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    padding: var(--mobile-spacing-lg);
    transform: translateY(100%);
    transition: transform var(--transition-normal);
  }

  .quick-edit-panel.show {
    transform: translateY(0);
  }

  .quick-edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--mobile-spacing-lg);
    padding-bottom: var(--mobile-spacing-md);
    border-bottom: 2px solid var(--border-color);
  }

  .quick-edit-title {
    font-size: var(--font-size-mobile-lg);
    font-weight: 600;
    color: var(--text-primary);
  }

  .quick-edit-close {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
    border: none;
    background: var(--bg-secondary);
    border-radius: 50%;
    font-size: var(--font-size-mobile-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 小屏设备进一步优化 */
@media (max-width: 480px) {
  .order-actions .btn {
    min-width: 100%;
    margin-bottom: var(--mobile-spacing-xs);
  }

  .batch-actions .btn {
    height: var(--mobile-btn-height-xs); /* 进一步缩小 */
    min-width: 24px; /* 从28px减少到24px */
    padding: 0 var(--mobile-ultra-xs); /* 最小内边距 */
    font-size: 8px; /* 极小字体 */
  }

  .multi-order-footer {
    padding: var(--mobile-ultra-xs); /* 1px内边距 */
    gap: var(--mobile-ultra-xs); /* 1px间距 */
  }

  .creation-summary {
    padding: var(--mobile-ultra-xs); /* 1px内边距 */
  }

  .creation-summary span {
    font-size: 8px; /* 极小字体 */
    margin-bottom: 1px;
  }

  #createSelectedOrdersBtn {
    height: var(--mobile-btn-height-xs); /* 18px高度 */
    min-width: 40px; /* 进一步减少到40px */
    font-size: 7px; /* 更小字体 */
    padding: 0 var(--mobile-ultra-xs);
  }

  .multi-order-header h3 {
    font-size: var(--font-size-mobile-base);
  }
/* ======================== 批量控制面板样式 ======================== */

/* 批量控制面板基础样式 */
.batch-control-panel {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  box-shadow: var(--neu-shadow-inset);
  margin-bottom: var(--spacing-6);
  border: 1px solid var(--border-color);
}

.batch-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.batch-control-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.batch-control-toggle {
  background: var(--bg-secondary);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--neu-shadow-subtle);
}

.batch-control-toggle:hover {
  box-shadow: var(--neu-shadow-hover);
  background: var(--color-gray-100);
}

.batch-control-content {
  padding: var(--spacing-6);
}

.batch-control-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}

.batch-control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.batch-control-group.batch-control-actions {
  grid-column: 1 / -1;
  flex-direction: row;
  justify-content: center;
  gap: var(--spacing-4);
}

.batch-control-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.batch-control-select {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition-fast);
  box-shadow: var(--neu-shadow-inset);
}

.batch-control-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.1);
}

.batch-control-select[multiple] {
  min-height: 80px;
}

.batch-apply-btn, .batch-clear-btn, .batch-reset-btn {
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--neu-shadow-outset);
}

.batch-apply-btn {
  background: var(--color-primary-gradient);
  color: white;
}

.batch-apply-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--neu-shadow-hover);
}

.batch-clear-btn {
  background: var(--color-warning);
  color: white;
}

.batch-reset-btn {
  background: var(--color-gray-500);
  color: white;
}

.batch-status {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* ======================== Paging订单特殊样式 ======================== */

/* Paging订单徽章 */
.paging-badge {
  background: var(--color-warning);
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  margin-left: var(--spacing-2);
}

/* Paging订单项样式 */
.order-item.paging-order {
  border: 2px solid var(--color-warning-light);
  background: linear-gradient(135deg, var(--color-warning-light) 0%, rgba(255, 152, 0, 0.05) 100%);
}

.order-item.paging-order .order-header {
  background: var(--color-warning-light);
}

/* Paging订单摘要样式 */
.order-summary-grid.paging-order-summary {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 152, 0, 0.02) 100%);
  border: 1px solid var(--color-warning-light);
}

.paging-info {
  background: var(--color-warning-light);
  color: var(--color-warning);
  grid-column: 1 / -1;
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: 0.8rem;
  margin-top: var(--spacing-2);
}

.paging-stats {
  background: var(--color-warning-light);
  color: var(--color-warning);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: 500;
  margin-left: var(--spacing-3);
}

/* ======================== 移动端优化样式 - 极致紧凑重构版 ======================== */

@media (max-width: 768px) {
  /* 全新移动端变量 - 极致紧凑设计 */
  :root {
    /* 超紧凑间距系统 */
    --mobile-ultra-xs: 1px;     /* 1px */
    --mobile-ultra-sm: 2px;     /* 2px */
    --mobile-ultra-md: 4px;     /* 4px */
    --mobile-ultra-lg: 6px;     /* 6px */
    --mobile-ultra-xl: 8px;     /* 8px */
    
    /* 极小按钮尺寸 */
    --mobile-btn-height-xs: 20px;
    --mobile-btn-height-sm: 24px;
    --mobile-btn-height-md: 28px;
    
    /* 紧凑字体 */
    --mobile-compact-xs: 10px;
    --mobile-compact-sm: 11px;
    --mobile-compact-md: 12px;
    --mobile-compact-lg: 13px;
  }

  /* 批量控制面板 - 极致紧凑重构 */
  .batch-control-panel {
    margin-bottom: var(--mobile-ultra-md);
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
  }
  
  .batch-control-header {
    padding: var(--mobile-ultra-md) var(--mobile-ultra-lg);
    flex-direction: row;
    gap: var(--mobile-ultra-md);
    min-height: auto;
    background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  }
  
  .batch-control-header h4 {
    font-size: var(--mobile-compact-md);
    font-weight: 600;
    margin: 0;
    flex: 1;
    line-height: 1.2;
  }
  
  .batch-control-toggle {
    font-size: var(--mobile-compact-xs);
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-md);
    height: var(--mobile-btn-height-xs);
    border-radius: var(--mobile-ultra-sm);
    min-width: 30px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    line-height: 1;
  }
  
  .batch-control-content {
    padding: var(--mobile-ultra-lg) var(--mobile-ultra-lg);
  }
  
  .batch-control-grid {
    grid-template-columns: repeat(3, 1fr); /* 改为三列布局 */
    gap: var(--mobile-ultra-md);
  }
  
  .batch-control-group {
    gap: var(--mobile-ultra-md);
  }
  
  .batch-control-label {
    font-size: var(--mobile-compact-sm);
    font-weight: 500;
    margin-bottom: var(--mobile-ultra-xs);
    line-height: 1.2;
  }
  
  .batch-control-select {
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-sm);
    height: var(--mobile-btn-height-md);
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
    line-height: 1.2;
  }
  
  .batch-control-select[multiple] {
    height: 60px; /* 大幅减少多选框高度 */
  }
  
  /* 批量操作按钮 - 跨列显示 */
  .batch-control-group.batch-control-actions {
    grid-column: 1 / -1; /* 跨所有三列 */
    flex-direction: row;
    justify-content: center;
    gap: var(--mobile-ultra-sm); /* 极小间距 */
    margin-top: var(--mobile-ultra-md);
  }
  
  .batch-apply-btn, .batch-clear-btn, .batch-reset-btn {
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-xs);
    height: var(--mobile-btn-height-sm);
    border-radius: var(--mobile-ultra-sm);
    font-weight: 500;
    border: 1px solid;
    min-width: 35px;
    line-height: 1;
    text-align: center;
  }
  
  .batch-apply-btn {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
  }
  
  .batch-clear-btn {
    background: var(--color-warning);
    color: white;
    border-color: var(--color-warning);
  }
  
  .batch-reset-btn {
    background: var(--color-gray-500);
    color: white;
    border-color: var(--color-gray-500);
  }
  
  /* 批量控制面板语言多选组件 - 移动端特化 */
  .batch-language-select .multi-select-trigger {
    min-height: var(--mobile-btn-height-md);
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-sm);
    line-height: 1.2;
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
  }
  
  .batch-language-select .multi-select-text {
    font-size: var(--mobile-compact-sm);
    line-height: 1.2;
  }
  
  .batch-language-select .multi-select-arrow {
    font-size: var(--mobile-compact-xs);
    margin-left: var(--mobile-ultra-sm);
  }
  
  .batch-language-select .multi-select-options {
    max-height: 160px; /* 增加移动端高度以显示更多选项 */
    border-radius: var(--mobile-ultra-sm);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .batch-language-select .multi-select-option {
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
    min-height: 24px; /* 减少选项高度 */
    border-bottom: 1px solid var(--border-color);
  }
  
  .batch-language-select .multi-select-checkbox {
    width: 12px;
    height: 12px;
    margin-right: var(--mobile-ultra-sm);
  }
  
  .batch-language-select .multi-select-label {
    font-size: var(--mobile-compact-xs);
    line-height: 1.2;
  }

  .batch-status {
    font-size: var(--mobile-compact-xs);
    padding: var(--mobile-ultra-md);
    margin-top: var(--mobile-ultra-md);
    text-align: center;
    background: var(--color-gray-50);
    border-radius: var(--mobile-ultra-sm);
  }

  /* 订单摘要网格 - 超紧凑四列布局 */
  .order-summary-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 四列布局提升信息密度 */
    gap: var(--mobile-ultra-xs); /* 极小间距 */
    padding: var(--mobile-ultra-md);
    background: var(--bg-secondary);
    border-radius: var(--mobile-ultra-sm);
    margin: var(--mobile-ultra-md) 0;
  }
  
  .order-summary-item {
    padding: var(--mobile-ultra-sm);
    background: var(--bg-primary);
    border-radius: var(--mobile-ultra-xs);
    border: 1px solid var(--border-color);
    min-height: 30px; /* 大幅减少高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  
  .order-summary-label {
    font-size: var(--mobile-compact-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--mobile-ultra-xs);
    line-height: 1.1;
    text-transform: uppercase;
    letter-spacing: 0.2px;
  }
  
  .order-summary-value {
    font-size: var(--mobile-compact-sm);
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
    word-break: break-word;
  }

  /* 订单操作按钮 - 超紧凑设计 */
  .order-actions {
    padding: var(--mobile-ultra-md);
    gap: var(--mobile-ultra-sm);
    flex-wrap: wrap;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--mobile-ultra-sm);
  }

  .order-actions .btn {
    height: var(--mobile-btn-height-sm);
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-xs);
    font-weight: 500;
    min-width: 35px;
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
    line-height: 1;
  }

  /* 多订单底部操作区 - 超级紧凑设计 */
  .multi-order-footer {
    padding: var(--mobile-ultra-sm); /* 从6px减少到2px */
    flex-direction: column;
    gap: var(--mobile-ultra-sm); /* 从6px减少到2px */
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
  }

  .batch-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--mobile-ultra-xs); /* 从2px减少到1px */
  }

  .batch-actions .btn {
    height: var(--mobile-btn-height-xs); /* 从24px减少到20px */
    padding: 0 var(--mobile-ultra-sm); /* 极小内边距 */
    font-size: var(--mobile-compact-xs);
    font-weight: 500;
    min-width: 28px; /* 从35px减少到28px */
    border-radius: var(--mobile-ultra-sm);
    flex: 0 0 auto;
  }

  .creation-summary {
    text-align: center;
    padding: var(--mobile-ultra-sm); /* 从4px减少到2px */
    background: var(--bg-primary);
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
  }

  .creation-summary span {
    font-size: var(--mobile-compact-xs); /* 进一步缩小字体 */
    font-weight: 500;
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--mobile-ultra-xs); /* 从4px减少到1px */
  }

  #createSelectedOrdersBtn {
    height: var(--mobile-btn-height-sm); /* 从28px减少到24px */
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-md); /* 减少内边距 */
    font-size: var(--mobile-compact-xs); /* 进一步缩小字体 */
    font-weight: 600;
    border-radius: var(--mobile-ultra-sm);
    min-width: 60px; /* 从80px减少到60px */
  }

  /* 订单卡片 - 超紧凑设计 */
  .order-item {
    margin-bottom: var(--mobile-ultra-lg);
    border-radius: var(--mobile-ultra-sm);
    border: 1px solid var(--border-color);
  }

  .order-header {
    padding: var(--mobile-ultra-md) var(--mobile-ultra-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--mobile-ultra-sm) var(--mobile-ultra-sm) 0 0;
  }

  .order-selector input[type="checkbox"] {
    width: 14px;
    height: 14px;
    margin-right: var(--mobile-ultra-sm);
  }

  .order-selector label {
    font-size: var(--mobile-compact-sm);
    font-weight: 500;
    line-height: 1.2;
  }

  .status-badge {
    font-size: var(--mobile-compact-xs);
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-sm);
    border-radius: var(--mobile-ultra-sm);
    font-weight: 500;
  }

  /* Paging订单 - 紧凑设计 */
  .paging-badge {
    font-size: var(--mobile-compact-xs);
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-sm);
    margin-left: var(--mobile-ultra-sm);
    border-radius: var(--mobile-ultra-xs);
  }

  .paging-info {
    padding: var(--mobile-ultra-md);
    font-size: var(--mobile-compact-xs);
    gap: var(--mobile-ultra-sm);
    margin-top: var(--mobile-ultra-sm);
    border-radius: var(--mobile-ultra-sm);
  }

  .paging-stats {
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-xs);
    margin-left: var(--mobile-ultra-md);
    border-radius: var(--mobile-ultra-sm);
  }

  /* 字段编辑区域 - 超紧凑双列布局 */
  .order-fields-grid {
    padding: var(--mobile-ultra-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-ultra-md);
  }

  .order-field-group {
    margin-bottom: var(--mobile-ultra-sm);
  }

  .order-field-label {
    font-size: var(--mobile-compact-xs);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--mobile-ultra-xs);
    line-height: 1.1;
  }

  .order-field-input {
    height: var(--mobile-btn-height-md);
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
    font-size: var(--mobile-compact-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--mobile-ultra-sm);
    line-height: 1.2;
  }

  .order-field-group.full-width {
    grid-column: 1 / -1;
  }

  .textarea-input {
    height: 40px !important; /* 大幅减少文本域高度 */
    resize: vertical;
    line-height: 1.2;
  }

  /* 特殊项目跨列显示 */
  .order-summary-warning {
    grid-column: 1 / -1;
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid var(--color-warning);
    padding: var(--mobile-ultra-md);
    border-radius: var(--mobile-ultra-sm);
    text-align: center;
    margin-top: var(--mobile-ultra-md);
  }

  .warning-text {
    font-size: var(--mobile-compact-xs);
    color: var(--color-warning);
    font-weight: 500;
    line-height: 1.2;
  }
}

/* 超小屏设备进一步优化 */
@media (max-width: 480px) {
  :root {
    /* 更极致的紧凑设计 */
    --mobile-ultra-xs: 1px;
    --mobile-ultra-sm: 1px;
    --mobile-ultra-md: 2px;
    --mobile-ultra-lg: 3px;
    --mobile-ultra-xl: 4px;
    
    --mobile-btn-height-xs: 18px;
    --mobile-btn-height-sm: 20px;
    --mobile-btn-height-md: 24px;
    
    --mobile-compact-xs: 9px;
    --mobile-compact-sm: 10px;
    --mobile-compact-md: 11px;
    --mobile-compact-lg: 12px;
  }

  /* 批量控制面板超小屏优化 */
  .batch-control-header {
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
  }

  .batch-control-content {
    padding: var(--mobile-ultra-md);
  }

  .batch-control-grid {
    grid-template-columns: repeat(3, 1fr); /* 保持三列布局 */
    gap: var(--mobile-ultra-sm); /* 进一步减少间距 */
  }

  .batch-control-label {
    font-size: var(--mobile-compact-xs);
    margin-bottom: 1px;
  }

  .batch-control-select {
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-sm);
    font-size: var(--mobile-compact-xs);
    height: var(--mobile-btn-height-sm);
  }

  /* 订单摘要保持四列但间距更小 */
  .order-summary-grid {
    gap: 1px;
    padding: var(--mobile-ultra-sm);
  }

  .order-summary-item {
    min-height: 25px;
    padding: 1px;
  }

  /* 按钮进一步缩小 */
  .batch-apply-btn, .batch-clear-btn, .batch-reset-btn {
    height: var(--mobile-btn-height-xs);
    min-width: 28px;
    padding: 0 var(--mobile-ultra-sm);
  }

  .order-actions .btn {
    height: var(--mobile-btn-height-xs);
    min-width: 28px;
    padding: 0 var(--mobile-ultra-sm);
  }

  /* 字段编辑区域 */
  .order-field-input {
    height: var(--mobile-btn-height-sm);
    padding: var(--mobile-ultra-xs) var(--mobile-ultra-sm);
  }

  .textarea-input {
    height: 30px !important;
  }
}

/* 扩展小屏设备优化 */
@media (max-width: 480px) {
  /* 批量控制面板超小屏优化 */
  .batch-control-header {
    flex-direction: column;
    gap: var(--mobile-spacing-sm);
    text-align: center;
    padding: var(--mobile-spacing-sm);
  }
  
  .batch-control-header h4 {
    font-size: var(--multi-order-font-base);
  }
  
  .batch-control-toggle {
    align-self: center;
    font-size: var(--multi-order-font-sm);
    padding: var(--mobile-spacing-xs);
    min-height: 28px; /* 更小的按钮 */
  }
  
  .batch-control-content {
    padding: var(--mobile-spacing-sm);
  }
  
  .batch-control-label {
    font-size: var(--multi-order-font-sm);
  }
  
  .batch-control-select {
    font-size: var(--multi-order-font-sm);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    min-height: 32px; /* 更小的输入框 */
  }
  
  .batch-apply-btn, .batch-clear-btn, .batch-reset-btn {
    font-size: var(--multi-order-font-xs); /* 更小的按钮字体 */
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    min-height: 32px; /* 更小的按钮高度 */
  }
  
  /* 订单摘要超小屏优化 - 保持三列但调整间距 */
  .order-summary-grid {
    grid-template-columns: repeat(3, 1fr); /* 保持三列 */
    gap: var(--mobile-spacing-xs); /* 缩小间距 */
    padding: var(--mobile-spacing-sm);
  }
  
  .order-summary-item {
    padding: var(--mobile-spacing-xs);
    min-height: 40px; /* 缩小高度 */
  }
  
  .order-summary-label {
    font-size: var(--multi-order-font-xs);
    margin-bottom: 1px;
  }
  
  .order-summary-value {
    font-size: var(--multi-order-font-xs); /* 更小的值字体 */
    line-height: 1.2;
  }
  
  /* Paging订单超小屏优化 */
  .paging-badge {
    font-size: var(--multi-order-font-xs);
    padding: 1px var(--mobile-spacing-xs);
  }
  
  .paging-info {
    font-size: var(--multi-order-font-xs);
    padding: var(--mobile-spacing-sm);
  }
  
  .paging-stats {
    font-size: var(--multi-order-font-xs);
    padding: var(--mobile-spacing-xs);
  }
}