<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新三列布局测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试专用样式 */
        .test-content {
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-header {
            background: var(--color-primary);
            color: white;
            padding: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 新三列布局测试页面</h1>
        <p>测试新设计的毛玻璃三列布局系统</p>
    </div>

    <div class="workspace">
        <!-- 三列布局容器 -->
        <form id="testForm" class="three-column-layout">
            <!-- 左列：订单输入 + 行程信息 -->
            <div class="column-left column-mobile">
                <!-- 订单输入板块 -->
                <section class="panel compact-card" data-panel="order-input">
                    <div class="section-header">
                        <h3>📝 订单输入</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="test-content">
                            <div class="inline-item">
                                <span class="inline-label">状态:</span>
                                <span class="inline-value">正常</span>
                            </div>
                            <div class="inline-item">
                                <span class="inline-label">输入:</span>
                                <span class="inline-value">示例文本</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="testInput">测试输入:</label>
                            <input type="text" id="testInput" placeholder="请输入测试内容">
                        </div>
                    </div>
                </section>

                <!-- 行程信息板块 -->
                <section class="panel compact-card" data-panel="trip-info">
                    <div class="section-header">
                        <h3>🚗 行程信息</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="form-group">
                            <label for="pickup">出发地:</label>
                            <input type="text" id="pickup" placeholder="出发地点">
                        </div>
                        <div class="form-group">
                            <label for="dropoff">目的地:</label>
                            <input type="text" id="dropoff" placeholder="目的地点">
                        </div>
                    </div>
                </section>
            </div>

            <!-- 中列：实时预览 + 客户信息 -->
            <div class="column-middle column-mobile">
                <!-- 实时预览板块 -->
                <section class="panel compact-card" data-panel="realtime-preview">
                    <div class="section-header">
                        <h3>👁️ 实时预览</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="inline-item">
                            <span class="inline-label">解析状态:</span>
                            <span class="inline-value status-complete">✅ 成功</span>
                        </div>
                        <div class="inline-item">
                            <span class="inline-label">价格:</span>
                            <span class="inline-value">RM150.00</span>
                        </div>
                        <div class="inline-item">
                            <span class="inline-label">路线:</span>
                            <span class="inline-value">KLIA → 酒店</span>
                        </div>
                    </div>
                </section>

                <!-- 客户信息板块 -->
                <section class="panel compact-card" data-panel="customer-info">
                    <div class="section-header">
                        <h3>👤 客户信息</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="form-group">
                            <label for="customerName">客户姓名:</label>
                            <input type="text" id="customerName" placeholder="客户姓名">
                        </div>
                        <div class="form-group">
                            <label for="customerPhone">联系电话:</label>
                            <input type="tel" id="customerPhone" placeholder="联系电话">
                        </div>
                    </div>
                </section>
            </div>

            <!-- 右列：智能识别 + 服务配置 -->
            <div class="column-right column-mobile">
                <!-- 智能识别板块 -->
                <section class="panel compact-card" data-panel="ai-recognition">
                    <div class="section-header">
                        <h3>🤖 智能识别</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="inline-item">
                            <span class="inline-label">OTA类型:</span>
                            <span class="inline-value">Chong Dealer</span>
                        </div>
                        <div class="inline-item">
                            <span class="inline-label">语言:</span>
                            <span class="inline-value">中文</span>
                        </div>
                        <div class="inline-item">
                            <span class="inline-label">置信度:</span>
                            <span class="inline-value status-complete">95%</span>
                        </div>
                    </div>
                </section>

                <!-- 服务配置板块 -->
                <section class="panel compact-card" data-panel="service-config">
                    <div class="section-header">
                        <h3>⚙️ 服务配置</h3>
                    </div>
                    <div class="panel-content compact-inline-layout">
                        <div class="form-group">
                            <label for="carType">车型:</label>
                            <select id="carType">
                                <option>舒适5座</option>
                                <option>豪华轿车</option>
                                <option>MPV7座</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="language">语言:</label>
                            <select id="language">
                                <option>中文</option>
                                <option>English</option>
                                <option>Bahasa</option>
                            </select>
                        </div>
                    </div>
                </section>
            </div>
        </form>
    </div>

    <script>
        // 简单的测试脚本
        console.log('🧪 新三列布局测试页面已加载');
        
        // 测试响应式
        function testResponsive() {
            const layout = document.querySelector('.three-column-layout');
            if (layout) {
                console.log('✅ 三列布局容器已找到');
                console.log('✅ 当前视口宽度:', window.innerWidth);
                
                const columns = layout.querySelectorAll('.column-mobile');
                console.log('✅ 找到列数:', columns.length);
                
                // 测试毛玻璃效果
                const cards = layout.querySelectorAll('.compact-card');
                console.log('✅ 找到卡片数:', cards.length);
            }
        }
        
        // 页面加载完成后测试
        document.addEventListener('DOMContentLoaded', testResponsive);
        
        // 窗口大小改变时测试
        window.addEventListener('resize', testResponsive);
    </script>
</body>
</html>