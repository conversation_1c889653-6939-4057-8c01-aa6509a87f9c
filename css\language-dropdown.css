/* 语言选择下拉菜单样式 */
.language-selection-wrapper {
    position: relative;
    width: 100%;
}

.language-dropdown {
    position: relative;
    width: 100%;
}

.language-dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 4px;
    border: 1px solid var(--glass-border);
    border-radius: 4px;
    background: var(--bg-glass);
    cursor: pointer;
    min-height: 20px;
    box-sizing: border-box;
    font-size: var(--font-size, 11px);
}

.language-dropdown-header:hover {
    border-color: var(--color-primary);
    background-color: var(--brand-glass);
}

.dropdown-arrow {
    font-size: 10px;
    color: var(--color-primary);
    transition: transform 0.2s ease;
}

.language-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-glass);
    backdrop-filter: var(--blur-glass);
    border: 1px solid var(--glass-border);
    border-top: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
    max-height: 150px;
    overflow-y: auto;
    box-shadow: var(--shadow-card);
}

.language-checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.language-checkbox-item:last-child {
    border-bottom: none;
}

.language-checkbox-item:hover {
    background-color: #f8f9fa;
}

.language-checkbox-item input[type="checkbox"] {
    margin: 0;
    margin-right: 8px;
}

.checkmark {
    /* 可以在这里添加自定义复选框样式 */
}

.language-name {
    font-size: 14px;
    color: #333;
}

.language-checkbox-item input[type="checkbox"]:checked + .checkmark + .language-name {
    font-weight: 600;
    color: #007bff;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .language-dropdown-content {
        max-height: 150px;
    }
    
    .language-checkbox-item {
        padding: 12px;
    }
    
    .language-name {
        font-size: 16px;
    }
}